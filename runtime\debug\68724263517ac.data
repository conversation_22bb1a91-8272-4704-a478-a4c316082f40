a:14:{s:6:"config";s:4971:"a:5:{s:10:"phpVersion";s:5:"8.1.9";s:10:"yiiVersion";s:10:"2.0.52-dev";s:11:"application";a:8:{s:3:"yii";s:10:"2.0.52-dev";s:4:"name";s:14:"My Application";s:7:"version";s:3:"1.0";s:8:"language";s:2:"uz";s:14:"sourceLanguage";s:5:"en-US";s:7:"charset";s:5:"UTF-8";s:3:"env";s:3:"dev";s:5:"debug";b:1;}s:3:"php";a:5:{s:7:"version";s:5:"8.1.9";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;s:9:"memcached";b:0;}s:10:"extensions";a:16:{s:25:"kartik-v/yii2-krajee-base";a:3:{s:4:"name";s:25:"kartik-v/yii2-krajee-base";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/base";s:67:"D:\OSPanel\domains\silverzavod\vendor/kartik-v/yii2-krajee-base/src";}}s:20:"kartik-v/yii2-dialog";a:3:{s:4:"name";s:20:"kartik-v/yii2-dialog";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:14:"@kartik/dialog";s:62:"D:\OSPanel\domains\silverzavod\vendor/kartik-v/yii2-dialog/src";}}s:18:"kartik-v/yii2-grid";a:3:{s:4:"name";s:18:"kartik-v/yii2-grid";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/grid";s:60:"D:\OSPanel\domains\silverzavod\vendor/kartik-v/yii2-grid/src";}}s:35:"kartik-v/yii2-widget-datetimepicker";a:3:{s:4:"name";s:35:"kartik-v/yii2-widget-datetimepicker";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:16:"@kartik/datetime";s:77:"D:\OSPanel\domains\silverzavod\vendor/kartik-v/yii2-widget-datetimepicker/src";}}s:16:"yiisoft/yii2-jui";a:3:{s:4:"name";s:16:"yiisoft/yii2-jui";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:8:"@yii/jui";s:58:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-jui/src";}}s:33:"2amigos/yii2-arrayquery-component";a:3:{s:4:"name";s:33:"2amigos/yii2-arrayquery-component";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:21:"@dosamigos/arrayquery";s:75:"D:\OSPanel\domains\silverzavod\vendor/2amigos/yii2-arrayquery-component/src";}}s:17:"yii2mod/yii2-rbac";a:3:{s:4:"name";s:17:"yii2mod/yii2-rbac";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:13:"@yii2mod/rbac";s:55:"D:\OSPanel\domains\silverzavod\vendor/yii2mod/yii2-rbac";}}s:23:"yiisoft/yii2-bootstrap5";a:4:{s:4:"name";s:23:"yiisoft/yii2-bootstrap5";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:15:"@yii/bootstrap5";s:65:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-bootstrap5/src";}s:9:"bootstrap";s:40:"yii\bootstrap5\i18n\TranslationBootstrap";}s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:8:"2.1.25.0";s:5:"alias";a:1:{s:10:"@yii/debug";s:60:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-debug/src";}}s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:10:"@yii/faker";s:60:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-faker/src";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.2.6.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:58:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-gii/src";}}s:18:"yiisoft/yii2-queue";a:3:{s:4:"name";s:18:"yiisoft/yii2-queue";s:7:"version";s:23:"3.0.9999999.9999999-dev";s:5:"alias";a:10:{s:10:"@yii/queue";s:60:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src";s:13:"@yii/queue/db";s:71:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/db";s:14:"@yii/queue/sqs";s:72:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/sqs";s:15:"@yii/queue/file";s:73:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/file";s:15:"@yii/queue/sync";s:73:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/sync";s:16:"@yii/queue/redis";s:74:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/redis";s:16:"@yii/queue/stomp";s:74:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/stomp";s:18:"@yii/queue/gearman";s:76:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/gearman";s:20:"@yii/queue/beanstalk";s:78:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/beanstalk";s:23:"@yii/queue/amqp_interop";s:81:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/amqp_interop";}}s:26:"yiisoft/yii2-symfonymailer";a:3:{s:4:"name";s:26:"yiisoft/yii2-symfonymailer";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:18:"@yii/symfonymailer";s:68:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-symfonymailer/src";}}s:23:"yiisoft/yii2-bootstrap4";a:3:{s:4:"name";s:23:"yiisoft/yii2-bootstrap4";s:7:"version";s:8:"2.0.11.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap4";s:65:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-bootstrap4/src";}}s:20:"yii2mod/yii2-swagger";a:3:{s:4:"name";s:20:"yii2mod/yii2-swagger";s:7:"version";s:7:"1.0.0.0";s:5:"alias";a:1:{s:16:"@yii2mod/swagger";s:58:"D:\OSPanel\domains\silverzavod\vendor/yii2mod/yii2-swagger";}}s:23:"yiisoft/yii2-httpclient";a:3:{s:4:"name";s:23:"yiisoft/yii2-httpclient";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:15:"@yii/httpclient";s:65:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-httpclient/src";}}}}";s:3:"log";s:54336:"a:1:{s:8:"messages";a:67:{i:0;a:6:{i:0;s:68:"Bootstrap with yii\bootstrap5\i18n\TranslationBootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752318563.195645;i:4;a:0:{}i:5;i:2640128;}i:1;a:6:{i:0;s:33:"Bootstrap with yii\log\Dispatcher";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752318563.196502;i:4;a:0:{}i:5;i:2758064;}i:2;a:6:{i:0;s:53:"Bootstrap with app\components\SessionTimeoutComponent";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752318563.196846;i:4;a:0:{}i:5;i:2799272;}i:3;a:6:{i:0;s:22:"Bootstrap with Closure";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752318563.19685;i:4;a:0:{}i:5;i:2799648;}i:4;a:6:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1752318563.209329;i:4;a:0:{}i:5;i:3944848;}i:5;a:6:{i:0;s:15:"Session started";i:1;i:4;i:2;s:21:"yii\web\Session::open";i:3;d:1752318563.231519;i:4;a:0:{}i:5;i:4755336;}i:6;a:6:{i:0;s:74:"Opening DB connection: pgsql:host=127.0.0.1;port=5432;dbname=silver_test_1";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:1752318563.261168;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6059984;}i:9;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.307642;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6197408;}i:12;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.32382;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6307728;}i:15;a:6:{i:0;s:63:"SELECT * FROM "users" WHERE ("id"=1) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.327716;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6602496;}i:18;a:6:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752318563.333446;i:4;a:0:{}i:5;i:7292824;}i:19;a:6:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1752318563.335452;i:4;a:0:{}i:5;i:8076736;}i:20;a:6:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752318563.335642;i:4;a:0:{}i:5;i:8101568;}i:45;a:6:{i:0;s:41:"Route requested: 'backend/expenses/index'";i:1;i:8;i:2;s:34:"yii\web\Application::handleRequest";i:3;d:1752318563.33599;i:4;a:0:{}i:5;i:8156232;}i:46;a:6:{i:0;s:23:"Loading module: backend";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1752318563.335995;i:4;a:0:{}i:5;i:8157856;}i:47;a:6:{i:0;s:36:"Route to run: backend/expenses/index";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:1752318563.33723;i:4;a:0:{}i:5;i:8326232;}i:48;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='1'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.33966;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8806728;}i:51;a:6:{i:0;s:46:"SELECT * FROM "auth_item" WHERE "name"='admin'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.342523;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8813344;}i:54;a:6:{i:0;s:20:"Checking role: admin";i:1;i:8;i:2;s:40:"yii\rbac\DbManager::checkAccessRecursive";i:3;d:1752318563.344188;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8816720;}i:55;a:6:{i:0;s:81:"Running action: app\modules\backend\controllers\ExpensesController::actionIndex()";i:1;i:8;i:2;s:36:"yii\base\InlineAction::runWithParams";i:3;d:1752318563.344208;i:4;a:0:{}i:5;i:8815872;}i:56;a:6:{i:0;s:428:"SELECT "expenses".*, "expenses_type"."name" AS "expense_type_name", "users"."full_name" AS "added_by_user" FROM "expenses" LEFT JOIN "expenses_type" ON expenses.expense_type_id = expenses_type.id LEFT JOIN "users" ON expenses.add_user_id = users.id WHERE ("expenses"."deleted_at" IS NULL) AND ("expenses"."created_at" >= '2025-07-12 00:00:00') AND ("expenses"."created_at" <= '2025-07-12 23:59:59') ORDER BY "expenses"."id" DESC";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.345128;i:4;a:1:{i:0;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:32;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8897784;}i:59;a:6:{i:0;s:2814:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'expenses'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.347859;i:4;a:1:{i:0;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:32;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8921072;}i:62;a:6:{i:0;s:876:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='expenses'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.350771;i:4;a:1:{i:0;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:32;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8931752;}i:65;a:6:{i:0;s:56:"SELECT * FROM "expenses_type" WHERE "deleted_at" IS NULL";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.352746;i:4;a:1:{i:0;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:36;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8960720;}i:68;a:6:{i:0;s:2819:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'expenses_type'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.352997;i:4;a:1:{i:0;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:36;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8981616;}i:71;a:6:{i:0;s:881:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='expenses_type'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.355838;i:4;a:1:{i:0;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:36;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8988792;}i:74;a:6:{i:0;s:92:"Rendering view file: D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1752318563.358807;i:4;a:1:{i:0;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9135624;}i:75;a:6:{i:0;s:126:"
        SELECT balance 
        FROM cashbox 
        WHERE title = 'Оля' AND deleted_at IS NULL
        LIMIT 1
    ";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.360348;i:4;a:2:{i:0;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:21;s:8:"function";s:8:"queryOne";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9390864;}i:78;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=151";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.364609;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9876744;}i:81;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=151) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.365683;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9942792;}i:84;a:6:{i:0;s:2814:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'tracking'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.3764;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9951832;}i:87;a:6:{i:0;s:876:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='tracking'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.38016;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9962632;}i:90;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=150";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.381958;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9964520;}i:93;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=150) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.382275;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9976664;}i:96;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=149";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.382845;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9978312;}i:99;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=149) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.383097;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9990456;}i:102;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=148";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.383617;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9992104;}i:105;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=148) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.383838;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10004248;}i:108;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=147";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.384337;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10022280;}i:111;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=147) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.384554;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10034424;}i:114;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=146";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.385035;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10036072;}i:117;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=146) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.385553;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10048216;}i:120;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=103";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.386145;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10049864;}i:123;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=103) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.386402;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10062008;}i:126;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=102";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.387129;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10063656;}i:129;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=102) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.387459;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10079896;}i:132;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=89";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.387979;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10081544;}i:135;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=89) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.388213;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10093688;}i:138;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=88";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.388707;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10095336;}i:141;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=88) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.388924;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10107480;}i:144;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=85";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.389412;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10113224;}i:147;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=85) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.389628;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10125368;}i:150;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=84";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.390104;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10127016;}i:153;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=84) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.390722;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10139160;}i:156;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=83";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.391711;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10157192;}i:159;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=83) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.391988;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10169336;}i:162;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=82";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.392515;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10170984;}i:165;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=82) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.392751;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10183128;}i:168;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=81";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.393256;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10184776;}i:171;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=81) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.393481;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10196920;}i:174;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=80";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.39408;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10198568;}i:177;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=80) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.394565;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10210712;}i:180;a:6:{i:0;s:74:"Rendering view file: D:\OSPanel\domains\silverzavod\views\layouts\main.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1752318563.396303;i:4;a:1:{i:0;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10238584;}i:181;a:6:{i:0;s:74:"Rendering view file: D:\OSPanel\domains\silverzavod\views\layouts\menu.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1752318563.396649;i:4;a:2:{i:0;a:5:{s:4:"file";s:53:"D:\OSPanel\domains\silverzavod\views\layouts\main.php";s:4:"line";i:23;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10298792;}i:182;a:6:{i:0;s:89:"Rendering view file: D:\OSPanel\domains\silverzavod\views\layouts\partials/menu-admin.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1752318563.397614;i:4;a:3:{i:0;a:5:{s:4:"file";s:53:"D:\OSPanel\domains\silverzavod\views\layouts\menu.php";s:4:"line";i:54;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:53:"D:\OSPanel\domains\silverzavod\views\layouts\main.php";s:4:"line";i:23;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10338184;}i:183;a:6:{i:0;s:78:"Rendering view file: D:\OSPanel\domains\silverzavod\views\layouts\sub-menu.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1752318563.401618;i:4;a:2:{i:0;a:5:{s:4:"file";s:53:"D:\OSPanel\domains\silverzavod\views\layouts\main.php";s:4:"line";i:43;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10329424;}i:184;a:6:{i:0;s:75:"Rendering view file: D:\OSPanel\domains\silverzavod\views\layouts\modal.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1752318563.402861;i:4;a:2:{i:0;a:5:{s:4:"file";s:53:"D:\OSPanel\domains\silverzavod\views\layouts\main.php";s:4:"line";i:56;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10375904;}}}";s:9:"profiling";s:97265:"a:3:{s:6:"memory";i:10677576;s:4:"time";d:0.22613286972045898;s:8:"messages";a:94:{i:7;a:6:{i:0;s:74:"Opening DB connection: pgsql:host=127.0.0.1;port=5432;dbname=silver_test_1";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:1752318563.261235;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6061112;}i:8;a:6:{i:0;s:74:"Opening DB connection: pgsql:host=127.0.0.1;port=5432;dbname=silver_test_1";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:1752318563.305484;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6063048;}i:10;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.307685;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6198984;}i:11;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.322738;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6214344;}i:13;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.323845;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6309216;}i:14;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.325689;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6310776;}i:16;a:6:{i:0;s:63:"SELECT * FROM "users" WHERE ("id"=1) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.327743;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6603840;}i:17;a:6:{i:0;s:63:"SELECT * FROM "users" WHERE ("id"=1) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.328879;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6606848;}i:49;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='1'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.339679;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8809336;}i:50;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='1'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.341989;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8811496;}i:52;a:6:{i:0;s:46:"SELECT * FROM "auth_item" WHERE "name"='admin'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.34254;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8815952;}i:53;a:6:{i:0;s:46:"SELECT * FROM "auth_item" WHERE "name"='admin'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.343843;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8818048;}i:57;a:6:{i:0;s:428:"SELECT "expenses".*, "expenses_type"."name" AS "expense_type_name", "users"."full_name" AS "added_by_user" FROM "expenses" LEFT JOIN "expenses_type" ON expenses.expense_type_id = expenses_type.id LEFT JOIN "users" ON expenses.add_user_id = users.id WHERE ("expenses"."deleted_at" IS NULL) AND ("expenses"."created_at" >= '2025-07-12 00:00:00') AND ("expenses"."created_at" <= '2025-07-12 23:59:59') ORDER BY "expenses"."id" DESC";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.345143;i:4;a:1:{i:0;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:32;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8900784;}i:58;a:6:{i:0;s:428:"SELECT "expenses".*, "expenses_type"."name" AS "expense_type_name", "users"."full_name" AS "added_by_user" FROM "expenses" LEFT JOIN "expenses_type" ON expenses.expense_type_id = expenses_type.id LEFT JOIN "users" ON expenses.add_user_id = users.id WHERE ("expenses"."deleted_at" IS NULL) AND ("expenses"."created_at" >= '2025-07-12 00:00:00') AND ("expenses"."created_at" <= '2025-07-12 23:59:59') ORDER BY "expenses"."id" DESC";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.34773;i:4;a:1:{i:0;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:32;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8917296;}i:60;a:6:{i:0;s:2814:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'expenses'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.347879;i:4;a:1:{i:0;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:32;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8922560;}i:61;a:6:{i:0;s:2814:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'expenses'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.350609;i:4;a:1:{i:0;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:32;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8942688;}i:63;a:6:{i:0;s:876:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='expenses'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.350782;i:4;a:1:{i:0;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:32;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8933240;}i:64;a:6:{i:0;s:876:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='expenses'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.352385;i:4;a:1:{i:0;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:32;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8937400;}i:66;a:6:{i:0;s:56:"SELECT * FROM "expenses_type" WHERE "deleted_at" IS NULL";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.352756;i:4;a:1:{i:0;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:36;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8962208;}i:67;a:6:{i:0;s:56:"SELECT * FROM "expenses_type" WHERE "deleted_at" IS NULL";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.352919;i:4;a:1:{i:0;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:36;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8973368;}i:69;a:6:{i:0;s:2819:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'expenses_type'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.353011;i:4;a:1:{i:0;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:36;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8983104;}i:70;a:6:{i:0;s:2819:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'expenses_type'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.355588;i:4;a:1:{i:0;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:36;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8992328;}i:72;a:6:{i:0;s:881:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='expenses_type'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.355855;i:4;a:1:{i:0;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:36;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8990280;}i:73;a:6:{i:0;s:881:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='expenses_type'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.35744;i:4;a:1:{i:0;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:36;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8991840;}i:76;a:6:{i:0;s:126:"
        SELECT balance 
        FROM cashbox 
        WHERE title = 'Оля' AND deleted_at IS NULL
        LIMIT 1
    ";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.360364;i:4;a:2:{i:0;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:21;s:8:"function";s:8:"queryOne";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9393288;}i:77;a:6:{i:0;s:126:"
        SELECT balance 
        FROM cashbox 
        WHERE title = 'Оля' AND deleted_at IS NULL
        LIMIT 1
    ";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.361238;i:4;a:2:{i:0;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:21;s:8:"function";s:8:"queryOne";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9394896;}i:79;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=151";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.364626;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9880104;}i:80;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=151";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.364914;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9883632;}i:82;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=151) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.3657;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9946464;}i:83;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=151) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.376194;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9949608;}i:85;a:6:{i:0;s:2814:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'tracking'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.376428;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9954072;}i:86;a:6:{i:0;s:2814:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'tracking'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.379935;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9968624;}i:88;a:6:{i:0;s:876:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='tracking'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.380177;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9964872;}i:89;a:6:{i:0;s:876:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='tracking'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.381733;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9967184;}i:91;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=150";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.38197;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9967880;}i:92;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=150";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.382136;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9971408;}i:94;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=150) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.382288;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9980336;}i:95;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=150) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.382723;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9983480;}i:97;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=149";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.382854;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9981672;}i:98;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=149";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.382979;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9985200;}i:100;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=149) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.383108;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9994128;}i:101;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=149) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.383514;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9997272;}i:103;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=148";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.383625;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9995464;}i:104;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=148";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.383741;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9998992;}i:106;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=148) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.383848;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10007920;}i:107;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=148) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.384237;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10011064;}i:109;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=147";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.384345;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10025640;}i:110;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=147";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.384457;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10029168;}i:112;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=147) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.384564;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10038096;}i:113;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=147) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.384912;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10041240;}i:115;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=146";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.385043;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10039432;}i:116;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=146";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.38533;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10042960;}i:118;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=146) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.385571;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10051888;}i:119;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=146) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.386003;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10055032;}i:121;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=103";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.386155;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10053224;}i:122;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=103";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.386292;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10056752;}i:124;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=103) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.386414;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10065680;}i:125;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=103) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.386853;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10068824;}i:127;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=102";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.38715;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10067016;}i:128;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=102";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.387333;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10070544;}i:130;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=102) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.387471;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10083568;}i:131;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=102) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.387873;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10086712;}i:133;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=89";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.387987;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10084904;}i:134;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=89";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.388104;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10088432;}i:136;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=89) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.388224;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10097360;}i:137;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=89) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.388589;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10100504;}i:139;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=88";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.388716;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10098696;}i:140;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=88";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.388828;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10102224;}i:142;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=88) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.388935;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10111152;}i:143;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=88) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.389316;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10114296;}i:145;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=85";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.38942;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10116584;}i:146;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=85";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.389532;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10120112;}i:148;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=85) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.389639;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10129040;}i:149;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=85) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.389987;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10132184;}i:151;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=84";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.390113;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10130376;}i:152;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=84";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.390222;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10133904;}i:154;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=84) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.390792;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10142832;}i:155;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=84) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.391554;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10145976;}i:157;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=83";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.391723;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10160552;}i:158;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=83";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.391867;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10164080;}i:160;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=83) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.392001;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10173008;}i:161;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=83) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.392408;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10176152;}i:163;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=82";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.392524;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10174344;}i:164;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=82";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.392646;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10177872;}i:166;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=82) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.392762;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10186800;}i:167;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=82) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.393157;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10189944;}i:169;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=81";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.393265;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10188136;}i:170;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=81";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.393383;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10191664;}i:172;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=81) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.393492;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10200592;}i:173;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=81) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.393936;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10203736;}i:175;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=80";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.39409;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10201928;}i:176;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=80";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.394409;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10205456;}i:178;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=80) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.394578;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10214384;}i:179;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=80) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.394985;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10217528;}}}";s:2:"db";s:96430:"a:1:{s:8:"messages";a:92:{i:10;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.307685;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6198984;}i:11;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.322738;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6214344;}i:13;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.323845;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6309216;}i:14;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.325689;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6310776;}i:16;a:6:{i:0;s:63:"SELECT * FROM "users" WHERE ("id"=1) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.327743;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6603840;}i:17;a:6:{i:0;s:63:"SELECT * FROM "users" WHERE ("id"=1) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.328879;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6606848;}i:49;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='1'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.339679;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8809336;}i:50;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='1'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.341989;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8811496;}i:52;a:6:{i:0;s:46:"SELECT * FROM "auth_item" WHERE "name"='admin'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.34254;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8815952;}i:53;a:6:{i:0;s:46:"SELECT * FROM "auth_item" WHERE "name"='admin'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.343843;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8818048;}i:57;a:6:{i:0;s:428:"SELECT "expenses".*, "expenses_type"."name" AS "expense_type_name", "users"."full_name" AS "added_by_user" FROM "expenses" LEFT JOIN "expenses_type" ON expenses.expense_type_id = expenses_type.id LEFT JOIN "users" ON expenses.add_user_id = users.id WHERE ("expenses"."deleted_at" IS NULL) AND ("expenses"."created_at" >= '2025-07-12 00:00:00') AND ("expenses"."created_at" <= '2025-07-12 23:59:59') ORDER BY "expenses"."id" DESC";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.345143;i:4;a:1:{i:0;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:32;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8900784;}i:58;a:6:{i:0;s:428:"SELECT "expenses".*, "expenses_type"."name" AS "expense_type_name", "users"."full_name" AS "added_by_user" FROM "expenses" LEFT JOIN "expenses_type" ON expenses.expense_type_id = expenses_type.id LEFT JOIN "users" ON expenses.add_user_id = users.id WHERE ("expenses"."deleted_at" IS NULL) AND ("expenses"."created_at" >= '2025-07-12 00:00:00') AND ("expenses"."created_at" <= '2025-07-12 23:59:59') ORDER BY "expenses"."id" DESC";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.34773;i:4;a:1:{i:0;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:32;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8917296;}i:60;a:6:{i:0;s:2814:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'expenses'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.347879;i:4;a:1:{i:0;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:32;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8922560;}i:61;a:6:{i:0;s:2814:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'expenses'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.350609;i:4;a:1:{i:0;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:32;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8942688;}i:63;a:6:{i:0;s:876:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='expenses'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.350782;i:4;a:1:{i:0;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:32;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8933240;}i:64;a:6:{i:0;s:876:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='expenses'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.352385;i:4;a:1:{i:0;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:32;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8937400;}i:66;a:6:{i:0;s:56:"SELECT * FROM "expenses_type" WHERE "deleted_at" IS NULL";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.352756;i:4;a:1:{i:0;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:36;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8962208;}i:67;a:6:{i:0;s:56:"SELECT * FROM "expenses_type" WHERE "deleted_at" IS NULL";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.352919;i:4;a:1:{i:0;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:36;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8973368;}i:69;a:6:{i:0;s:2819:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'expenses_type'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.353011;i:4;a:1:{i:0;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:36;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8983104;}i:70;a:6:{i:0;s:2819:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'expenses_type'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.355588;i:4;a:1:{i:0;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:36;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8992328;}i:72;a:6:{i:0;s:881:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='expenses_type'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.355855;i:4;a:1:{i:0;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:36;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8990280;}i:73;a:6:{i:0;s:881:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='expenses_type'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.35744;i:4;a:1:{i:0;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:36;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8991840;}i:76;a:6:{i:0;s:126:"
        SELECT balance 
        FROM cashbox 
        WHERE title = 'Оля' AND deleted_at IS NULL
        LIMIT 1
    ";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.360364;i:4;a:2:{i:0;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:21;s:8:"function";s:8:"queryOne";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9393288;}i:77;a:6:{i:0;s:126:"
        SELECT balance 
        FROM cashbox 
        WHERE title = 'Оля' AND deleted_at IS NULL
        LIMIT 1
    ";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.361238;i:4;a:2:{i:0;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:21;s:8:"function";s:8:"queryOne";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9394896;}i:79;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=151";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.364626;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9880104;}i:80;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=151";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.364914;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9883632;}i:82;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=151) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.3657;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9946464;}i:83;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=151) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.376194;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9949608;}i:85;a:6:{i:0;s:2814:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'tracking'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.376428;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9954072;}i:86;a:6:{i:0;s:2814:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'tracking'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.379935;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9968624;}i:88;a:6:{i:0;s:876:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='tracking'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.380177;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9964872;}i:89;a:6:{i:0;s:876:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='tracking'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.381733;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9967184;}i:91;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=150";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.38197;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9967880;}i:92;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=150";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.382136;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9971408;}i:94;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=150) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.382288;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9980336;}i:95;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=150) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.382723;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9983480;}i:97;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=149";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.382854;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9981672;}i:98;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=149";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.382979;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9985200;}i:100;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=149) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.383108;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9994128;}i:101;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=149) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.383514;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9997272;}i:103;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=148";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.383625;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9995464;}i:104;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=148";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.383741;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9998992;}i:106;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=148) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.383848;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10007920;}i:107;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=148) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.384237;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10011064;}i:109;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=147";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.384345;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10025640;}i:110;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=147";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.384457;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10029168;}i:112;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=147) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.384564;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10038096;}i:113;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=147) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.384912;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10041240;}i:115;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=146";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.385043;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10039432;}i:116;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=146";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.38533;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10042960;}i:118;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=146) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.385571;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10051888;}i:119;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=146) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.386003;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10055032;}i:121;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=103";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.386155;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10053224;}i:122;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=103";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.386292;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10056752;}i:124;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=103) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.386414;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10065680;}i:125;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=103) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.386853;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10068824;}i:127;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=102";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.38715;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10067016;}i:128;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=102";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.387333;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10070544;}i:130;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=102) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.387471;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10083568;}i:131;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=102) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.387873;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10086712;}i:133;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=89";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.387987;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10084904;}i:134;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=89";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.388104;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10088432;}i:136;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=89) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.388224;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10097360;}i:137;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=89) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.388589;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10100504;}i:139;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=88";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.388716;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10098696;}i:140;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=88";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.388828;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10102224;}i:142;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=88) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.388935;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10111152;}i:143;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=88) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.389316;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10114296;}i:145;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=85";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.38942;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10116584;}i:146;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=85";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.389532;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10120112;}i:148;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=85) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.389639;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10129040;}i:149;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=85) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.389987;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10132184;}i:151;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=84";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.390113;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10130376;}i:152;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=84";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.390222;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10133904;}i:154;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=84) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.390792;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10142832;}i:155;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=84) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.391554;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10145976;}i:157;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=83";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.391723;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10160552;}i:158;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=83";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.391867;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10164080;}i:160;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=83) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.392001;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10173008;}i:161;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=83) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.392408;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10176152;}i:163;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=82";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.392524;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10174344;}i:164;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=82";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.392646;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10177872;}i:166;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=82) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.392762;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10186800;}i:167;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=82) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.393157;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10189944;}i:169;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=81";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.393265;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10188136;}i:170;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=81";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.393383;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10191664;}i:172;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=81) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.393492;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10200592;}i:173;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=81) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.393936;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10203736;}i:175;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=80";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.39409;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10201928;}i:176;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=80";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.394409;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10205456;}i:178;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=80) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.394578;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10214384;}i:179;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=80) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318563.394985;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10217528;}}}";s:5:"event";s:34608:"a:190:{i:0;a:5:{s:4:"time";d:1752318563.252555;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:1;a:5:{s:4:"time";d:1752318563.305468;s:4:"name";s:9:"afterOpen";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:2;a:5:{s:4:"time";d:1752318563.328964;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:32:"app\modules\backend\models\Users";}i:3;a:5:{s:4:"time";d:1752318563.328991;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:32:"app\modules\backend\models\Users";}i:4;a:5:{s:4:"time";d:1752318563.33203;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:32:"app\modules\backend\models\Users";}i:5;a:5:{s:4:"time";d:1752318563.335746;s:4:"name";s:13:"beforeRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:6;a:5:{s:4:"time";d:1752318563.337302;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:7;a:5:{s:4:"time";d:1752318563.337309;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"app\modules\backend\BackendModule";}i:8;a:5:{s:4:"time";d:1752318563.338549;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:9;a:5:{s:4:"time";d:1752318563.33856;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:10;a:5:{s:4:"time";d:1752318563.338566;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:11;a:5:{s:4:"time";d:1752318563.33857;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:12;a:5:{s:4:"time";d:1752318563.338573;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:13;a:5:{s:4:"time";d:1752318563.338576;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:14;a:5:{s:4:"time";d:1752318563.33858;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:15;a:5:{s:4:"time";d:1752318563.338599;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:50:"app\modules\backend\controllers\ExpensesController";}i:16;a:5:{s:4:"time";d:1752318563.344504;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:17;a:5:{s:4:"time";d:1752318563.352707;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:18;a:5:{s:4:"time";d:1752318563.352968;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:19;a:5:{s:4:"time";d:1752318563.357553;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:20;a:5:{s:4:"time";d:1752318563.357563;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:21;a:5:{s:4:"time";d:1752318563.357569;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:22;a:5:{s:4:"time";d:1752318563.357575;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:23;a:5:{s:4:"time";d:1752318563.35758;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:24;a:5:{s:4:"time";d:1752318563.357584;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:25;a:5:{s:4:"time";d:1752318563.357589;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:26;a:5:{s:4:"time";d:1752318563.357594;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:27;a:5:{s:4:"time";d:1752318563.357599;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:28;a:5:{s:4:"time";d:1752318563.357604;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:29;a:5:{s:4:"time";d:1752318563.357609;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:30;a:5:{s:4:"time";d:1752318563.357613;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:31;a:5:{s:4:"time";d:1752318563.357618;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:32;a:5:{s:4:"time";d:1752318563.357623;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:33;a:5:{s:4:"time";d:1752318563.357628;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:34;a:5:{s:4:"time";d:1752318563.357633;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:35;a:5:{s:4:"time";d:1752318563.357638;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:36;a:5:{s:4:"time";d:1752318563.357644;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:37;a:5:{s:4:"time";d:1752318563.357647;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:38;a:5:{s:4:"time";d:1752318563.357649;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:39;a:5:{s:4:"time";d:1752318563.357651;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:40;a:5:{s:4:"time";d:1752318563.357653;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:41;a:5:{s:4:"time";d:1752318563.357655;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:42;a:5:{s:4:"time";d:1752318563.357657;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:43;a:5:{s:4:"time";d:1752318563.357659;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:44;a:5:{s:4:"time";d:1752318563.357661;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:45;a:5:{s:4:"time";d:1752318563.357663;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:46;a:5:{s:4:"time";d:1752318563.357665;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:47;a:5:{s:4:"time";d:1752318563.357667;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:48;a:5:{s:4:"time";d:1752318563.357669;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:49;a:5:{s:4:"time";d:1752318563.357671;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:50;a:5:{s:4:"time";d:1752318563.357673;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:51;a:5:{s:4:"time";d:1752318563.357675;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:52;a:5:{s:4:"time";d:1752318563.357677;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:53;a:5:{s:4:"time";d:1752318563.35768;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:54;a:5:{s:4:"time";d:1752318563.358803;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:55;a:5:{s:4:"time";d:1752318563.363315;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:56;a:5:{s:4:"time";d:1752318563.363328;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:57;a:5:{s:4:"time";d:1752318563.363333;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:58;a:5:{s:4:"time";d:1752318563.363337;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:59;a:5:{s:4:"time";d:1752318563.363341;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:60;a:5:{s:4:"time";d:1752318563.363345;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:61;a:5:{s:4:"time";d:1752318563.363349;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:62;a:5:{s:4:"time";d:1752318563.363353;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:63;a:5:{s:4:"time";d:1752318563.363357;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:64;a:5:{s:4:"time";d:1752318563.363361;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:65;a:5:{s:4:"time";d:1752318563.363365;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:66;a:5:{s:4:"time";d:1752318563.363369;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:67;a:5:{s:4:"time";d:1752318563.363373;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:68;a:5:{s:4:"time";d:1752318563.363807;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\widgets\Pjax";}i:69;a:5:{s:4:"time";d:1752318563.364547;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:70;a:5:{s:4:"time";d:1752318563.364968;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:71;a:5:{s:4:"time";d:1752318563.364992;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:72;a:5:{s:4:"time";d:1752318563.365211;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:73;a:5:{s:4:"time";d:1752318563.376333;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:74;a:5:{s:4:"time";d:1752318563.381847;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:75;a:5:{s:4:"time";d:1752318563.381906;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:76;a:5:{s:4:"time";d:1752318563.382185;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:77;a:5:{s:4:"time";d:1752318563.382204;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:78;a:5:{s:4:"time";d:1752318563.382216;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:79;a:5:{s:4:"time";d:1752318563.38277;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:80;a:5:{s:4:"time";d:1752318563.382783;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:81;a:5:{s:4:"time";d:1752318563.382815;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:82;a:5:{s:4:"time";d:1752318563.383021;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:83;a:5:{s:4:"time";d:1752318563.383035;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:84;a:5:{s:4:"time";d:1752318563.383053;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:85;a:5:{s:4:"time";d:1752318563.383557;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:86;a:5:{s:4:"time";d:1752318563.383568;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:87;a:5:{s:4:"time";d:1752318563.383593;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:88;a:5:{s:4:"time";d:1752318563.383782;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:89;a:5:{s:4:"time";d:1752318563.383794;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:90;a:5:{s:4:"time";d:1752318563.383801;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:91;a:5:{s:4:"time";d:1752318563.384279;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:92;a:5:{s:4:"time";d:1752318563.384289;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:93;a:5:{s:4:"time";d:1752318563.384314;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:94;a:5:{s:4:"time";d:1752318563.384498;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:95;a:5:{s:4:"time";d:1752318563.384509;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:96;a:5:{s:4:"time";d:1752318563.384516;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:97;a:5:{s:4:"time";d:1752318563.38498;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:98;a:5:{s:4:"time";d:1752318563.384991;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:99;a:5:{s:4:"time";d:1752318563.385014;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:100;a:5:{s:4:"time";d:1752318563.385454;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:101;a:5:{s:4:"time";d:1752318563.385477;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:102;a:5:{s:4:"time";d:1752318563.385491;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:103;a:5:{s:4:"time";d:1752318563.386063;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:104;a:5:{s:4:"time";d:1752318563.386079;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:105;a:5:{s:4:"time";d:1752318563.386112;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:106;a:5:{s:4:"time";d:1752318563.386335;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:107;a:5:{s:4:"time";d:1752318563.386349;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:108;a:5:{s:4:"time";d:1752318563.386358;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:109;a:5:{s:4:"time";d:1752318563.387008;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:110;a:5:{s:4:"time";d:1752318563.387033;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:111;a:5:{s:4:"time";d:1752318563.387081;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:112;a:5:{s:4:"time";d:1752318563.387385;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:113;a:5:{s:4:"time";d:1752318563.387401;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:114;a:5:{s:4:"time";d:1752318563.387411;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:115;a:5:{s:4:"time";d:1752318563.387916;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:116;a:5:{s:4:"time";d:1752318563.387928;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:117;a:5:{s:4:"time";d:1752318563.387955;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:118;a:5:{s:4:"time";d:1752318563.388145;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:119;a:5:{s:4:"time";d:1752318563.388157;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:120;a:5:{s:4:"time";d:1752318563.388164;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:121;a:5:{s:4:"time";d:1752318563.38865;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:122;a:5:{s:4:"time";d:1752318563.388661;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:123;a:5:{s:4:"time";d:1752318563.388685;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:124;a:5:{s:4:"time";d:1752318563.388868;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:125;a:5:{s:4:"time";d:1752318563.38888;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:126;a:5:{s:4:"time";d:1752318563.388887;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:127;a:5:{s:4:"time";d:1752318563.389357;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:128;a:5:{s:4:"time";d:1752318563.389367;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:129;a:5:{s:4:"time";d:1752318563.38939;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:130;a:5:{s:4:"time";d:1752318563.389572;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:131;a:5:{s:4:"time";d:1752318563.389584;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:132;a:5:{s:4:"time";d:1752318563.389591;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:133;a:5:{s:4:"time";d:1752318563.390049;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:134;a:5:{s:4:"time";d:1752318563.39006;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:135;a:5:{s:4:"time";d:1752318563.390083;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:136;a:5:{s:4:"time";d:1752318563.390394;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:137;a:5:{s:4:"time";d:1752318563.390455;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:138;a:5:{s:4:"time";d:1752318563.390502;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:139;a:5:{s:4:"time";d:1752318563.391622;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:140;a:5:{s:4:"time";d:1752318563.391641;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:141;a:5:{s:4:"time";d:1752318563.391676;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:142;a:5:{s:4:"time";d:1752318563.391912;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:143;a:5:{s:4:"time";d:1752318563.391928;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:144;a:5:{s:4:"time";d:1752318563.391937;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:145;a:5:{s:4:"time";d:1752318563.392452;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:146;a:5:{s:4:"time";d:1752318563.392464;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:147;a:5:{s:4:"time";d:1752318563.39249;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:148;a:5:{s:4:"time";d:1752318563.392688;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:149;a:5:{s:4:"time";d:1752318563.392701;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:150;a:5:{s:4:"time";d:1752318563.39271;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:151;a:5:{s:4:"time";d:1752318563.393199;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:152;a:5:{s:4:"time";d:1752318563.39321;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:153;a:5:{s:4:"time";d:1752318563.393234;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:154;a:5:{s:4:"time";d:1752318563.393423;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:155;a:5:{s:4:"time";d:1752318563.393436;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:156;a:5:{s:4:"time";d:1752318563.393443;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:157;a:5:{s:4:"time";d:1752318563.394008;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:158;a:5:{s:4:"time";d:1752318563.394022;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:159;a:5:{s:4:"time";d:1752318563.39405;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:160;a:5:{s:4:"time";d:1752318563.394494;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:161;a:5:{s:4:"time";d:1752318563.394509;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:162;a:5:{s:4:"time";d:1752318563.394518;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:163;a:5:{s:4:"time";d:1752318563.395029;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:164;a:5:{s:4:"time";d:1752318563.39504;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:165;a:5:{s:4:"time";d:1752318563.395235;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\widgets\Pjax";}i:166;a:5:{s:4:"time";d:1752318563.39611;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\widgets\Pjax";}i:167;a:5:{s:4:"time";d:1752318563.396126;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:168;a:5:{s:4:"time";d:1752318563.396181;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:169;a:5:{s:4:"time";d:1752318563.3963;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:170;a:5:{s:4:"time";d:1752318563.396414;s:4:"name";s:9:"beginPage";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:171;a:5:{s:4:"time";d:1752318563.396579;s:4:"name";s:9:"beginBody";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:172;a:5:{s:4:"time";d:1752318563.396647;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:173;a:5:{s:4:"time";d:1752318563.397598;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:174;a:5:{s:4:"time";d:1752318563.400861;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:175;a:5:{s:4:"time";d:1752318563.401122;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:176;a:5:{s:4:"time";d:1752318563.401607;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:177;a:5:{s:4:"time";d:1752318563.402352;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:178;a:5:{s:4:"time";d:1752318563.40285;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:179;a:5:{s:4:"time";d:1752318563.403476;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:180;a:5:{s:4:"time";d:1752318563.405043;s:4:"name";s:7:"endBody";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:181;a:5:{s:4:"time";d:1752318563.406091;s:4:"name";s:7:"endPage";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:182;a:5:{s:4:"time";d:1752318563.406841;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:183;a:5:{s:4:"time";d:1752318563.406915;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:50:"app\modules\backend\controllers\ExpensesController";}i:184;a:5:{s:4:"time";d:1752318563.406936;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"app\modules\backend\BackendModule";}i:185;a:5:{s:4:"time";d:1752318563.406953;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:186;a:5:{s:4:"time";d:1752318563.406976;s:4:"name";s:12:"afterRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:187;a:5:{s:4:"time";d:1752318563.406995;s:4:"name";s:10:"beforeSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:188;a:5:{s:4:"time";d:1752318563.407993;s:4:"name";s:12:"afterPrepare";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:189;a:5:{s:4:"time";d:1752318563.408751;s:4:"name";s:9:"afterSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}}";s:4:"mail";s:6:"a:0:{}";s:8:"timeline";s:92:"a:3:{s:5:"start";d:1752318563.184524;s:3:"end";d:1752318563.413613;s:6:"memory";i:10677576;}";s:4:"dump";s:6:"a:0:{}";s:6:"router";s:4865:"a:3:{s:8:"messages";a:24:{i:21;a:6:{i:0;a:3:{s:4:"rule";s:3:"gii";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752318563.335929;i:4;a:0:{}i:5;i:8137920;}i:22;a:6:{i:0;a:3:{s:4:"rule";s:12:"gii/<id:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752318563.335935;i:4;a:0:{}i:5;i:8138672;}i:23;a:6:{i:0;a:3:{s:4:"rule";s:41:"gii/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752318563.335938;i:4;a:0:{}i:5;i:8139424;}i:24;a:6:{i:0;a:3:{s:4:"rule";s:5:"debug";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752318563.335941;i:4;a:0:{}i:5;i:8140176;}i:25;a:6:{i:0;a:3:{s:4:"rule";s:43:"debug/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752318563.335943;i:4;a:0:{}i:5;i:8140928;}i:26;a:6:{i:0;a:3:{s:4:"rule";s:1:"/";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752318563.335945;i:4;a:0:{}i:5;i:8141680;}i:27;a:6:{i:0;a:3:{s:4:"rule";s:5:"login";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752318563.335947;i:4;a:0:{}i:5;i:8142432;}i:28;a:6:{i:0;a:3:{s:4:"rule";s:6:"logout";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752318563.335949;i:4;a:0:{}i:5;i:8143184;}i:29;a:6:{i:0;a:3:{s:4:"rule";s:13:"site/<action>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752318563.335951;i:4;a:0:{}i:5;i:8143936;}i:30;a:6:{i:0;a:3:{s:4:"rule";s:39:"api/<controller:[\w-]+>/<action:[\w-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752318563.335953;i:4;a:0:{}i:5;i:8144688;}i:31;a:6:{i:0;a:3:{s:4:"rule";s:23:"api/<controller:[\w-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752318563.335955;i:4;a:0:{}i:5;i:8145440;}i:32;a:6:{i:0;a:3:{s:4:"rule";s:12:"swagger/json";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752318563.335957;i:4;a:0:{}i:5;i:8146192;}i:33;a:6:{i:0;a:3:{s:4:"rule";s:10:"swagger/ui";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752318563.335959;i:4;a:0:{}i:5;i:8148224;}i:34;a:6:{i:0;a:3:{s:4:"rule";s:7:"swagger";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752318563.335961;i:4;a:0:{}i:5;i:8148976;}i:35;a:6:{i:0;a:3:{s:4:"rule";s:25:"backend/position/<action>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752318563.335963;i:4;a:0:{}i:5;i:8149728;}i:36;a:6:{i:0;a:3:{s:4:"rule";s:16:"backend/position";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752318563.335965;i:4;a:0:{}i:5;i:8150480;}i:37;a:6:{i:0;a:3:{s:4:"rule";s:32:"backend/equipment/<id:\d+>/parts";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752318563.335967;i:4;a:0:{}i:5;i:8151232;}i:38;a:6:{i:0;a:3:{s:4:"rule";s:60:"backend/equipment/defect-part/<equipmentId:\d+>/<partId:\d+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752318563.335969;i:4;a:0:{}i:5;i:8151984;}i:39;a:6:{i:0;a:3:{s:4:"rule";s:61:"backend/equipment/reserve-part/<equipmentId:\d+>/<partId:\d+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752318563.335971;i:4;a:0:{}i:5;i:8152736;}i:40;a:6:{i:0;a:3:{s:4:"rule";s:60:"backend/equipment/repair-part/<equipmentId:\d+>/<partId:\d+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752318563.335973;i:4;a:0:{}i:5;i:8153488;}i:41;a:6:{i:0;a:3:{s:4:"rule";s:62:"backend/equipment/activate-part/<equipmentId:\d+>/<partId:\d+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752318563.335975;i:4;a:0:{}i:5;i:8154240;}i:42;a:6:{i:0;a:3:{s:4:"rule";s:38:"backend/<controller>/<action>/<id:\d+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752318563.335977;i:4;a:0:{}i:5;i:8154992;}i:43;a:6:{i:0;s:59:"Request parsed with URL rule: backend/<controller>/<action>";i:1;i:8;i:2;s:29:"yii\web\UrlRule::parseRequest";i:3;d:1752318563.335984;i:4;a:0:{}i:5;i:8157056;}i:44;a:6:{i:0;a:3:{s:4:"rule";s:29:"backend/<controller>/<action>";s:5:"match";b:1;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752318563.335986;i:4;a:0:{}i:5;i:8156904;}}s:5:"route";s:22:"backend/expenses/index";s:6:"action";s:65:"app\modules\backend\controllers\ExpensesController::actionIndex()";}";s:7:"request";s:6718:"a:15:{s:7:"flashes";a:0:{}s:10:"statusCode";i:200;s:14:"requestHeaders";a:9:{s:4:"host";s:6:"silver";s:10:"connection";s:10:"keep-alive";s:25:"upgrade-insecure-requests";s:1:"1";s:10:"user-agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:6:"accept";s:135:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7";s:7:"referer";s:574:"http://silver/backend/worker-payment/index?worker_id=91&month=2025-07&payment_types%5B1%5D%5Bmethods%5D%5B%5D=1&payment_types%5B1%5D%5Bmethods%5D%5B%5D=4&payment_types%5B1%5D%5Bamount%5D=4%C2%A0177%C2%A0242&payment_types%5B3%5D%5Bmethods%5D%5B%5D=1&payment_types%5B3%5D%5Bamount%5D=100000&payment_types%5B4%5D%5Bmethods%5D%5B%5D=1&payment_types%5B4%5D%5Bamount%5D=100000&payment_types%5B5%5D%5Bmethods%5D%5B%5D=1&payment_types%5B5%5D%5Bamount%5D=100000&payment_types%5B7%5D%5Bmethods%5D%5B%5D=1&payment_types%5B7%5D%5Bamount%5D=100000&payment_types%5B6%5D%5Bamount%5D=100000";s:15:"accept-encoding";s:13:"gzip, deflate";s:15:"accept-language";s:65:"en-RU,en;q=0.9,ru-RU;q=0.8,ru;q=0.7,en-US;q=0.6,uz;q=0.5,hr;q=0.4";s:6:"cookie";s:539:"language=4e1a22a706b2b67a2ac68d2d85cb839925d09aed085ca214408c5498b87a9ea2a%3A2%3A%7Bi%3A0%3Bs%3A8%3A%22language%22%3Bi%3A1%3Bs%3A2%3A%22uz%22%3B%7D; PHPSESSID=tg16mblupj6u875l5vqjrkigi7pqr8f3; _identity=d3bbf1a40fbb07a5e71f4b657b0d9caaa7c20725eb7f86d2213424408f95f8caa%3A2%3A%7Bi%3A0%3Bs%3A9%3A%22_identity%22%3Bi%3A1%3Bs%3A16%3A%22%5B1%2Cnull%2C2592000%5D%22%3B%7D; _csrf=5de5c8623f8b1f477dee3e2ed0014819171dc78456feef3a897ed3d5ed083c16a%3A2%3A%7Bi%3A0%3Bs%3A5%3A%22_csrf%22%3Bi%3A1%3Bs%3A32%3A%22q6T-dtfJoJoonDmVVkMNyEhTWI1Kz4Cz%22%3B%7D";}s:15:"responseHeaders";a:8:{s:7:"Expires";s:29:"Thu, 19 Nov 1981 08:52:00 GMT";s:13:"Cache-Control";s:35:"no-store, no-cache, must-revalidate";s:6:"Pragma";s:8:"no-cache";s:12:"Content-Type";s:24:"text/html; charset=UTF-8";s:11:"X-Debug-Tag";s:13:"68724263517ac";s:16:"X-Debug-Duration";s:3:"224";s:12:"X-Debug-Link";s:37:"/debug/default/view?tag=68724263517ac";s:10:"Set-Cookie";s:260:"_identity=d3bbf1a40fbb07a5e71f4b657b0d9caaa7c20725eb7f86d2213424408f95f8caa%3A2%3A%7Bi%3A0%3Bs%3A9%3A%22_identity%22%3Bi%3A1%3Bs%3A16%3A%22%5B1%2Cnull%2C2592000%5D%22%3B%7D; expires=Mon, 11-Aug-2025 11:09:23 GMT; Max-Age=2592000; path=/; HttpOnly; SameSite=Lax";}s:5:"route";s:22:"backend/expenses/index";s:6:"action";s:65:"app\modules\backend\controllers\ExpensesController::actionIndex()";s:12:"actionParams";a:0:{}s:7:"general";a:5:{s:6:"method";s:3:"GET";s:6:"isAjax";b:0;s:6:"isPjax";b:0;s:7:"isFlash";b:0;s:18:"isSecureConnection";b:0;}s:11:"requestBody";a:0:{}s:6:"SERVER";a:38:{s:15:"REDIRECT_STATUS";s:3:"200";s:9:"HTTP_HOST";s:6:"silver";s:15:"HTTP_CONNECTION";s:10:"keep-alive";s:30:"HTTP_UPGRADE_INSECURE_REQUESTS";s:1:"1";s:15:"HTTP_USER_AGENT";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:11:"HTTP_ACCEPT";s:135:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7";s:12:"HTTP_REFERER";s:574:"http://silver/backend/worker-payment/index?worker_id=91&month=2025-07&payment_types%5B1%5D%5Bmethods%5D%5B%5D=1&payment_types%5B1%5D%5Bmethods%5D%5B%5D=4&payment_types%5B1%5D%5Bamount%5D=4%C2%A0177%C2%A0242&payment_types%5B3%5D%5Bmethods%5D%5B%5D=1&payment_types%5B3%5D%5Bamount%5D=100000&payment_types%5B4%5D%5Bmethods%5D%5B%5D=1&payment_types%5B4%5D%5Bamount%5D=100000&payment_types%5B5%5D%5Bmethods%5D%5B%5D=1&payment_types%5B5%5D%5Bamount%5D=100000&payment_types%5B7%5D%5Bmethods%5D%5B%5D=1&payment_types%5B7%5D%5Bamount%5D=100000&payment_types%5B6%5D%5Bamount%5D=100000";s:20:"HTTP_ACCEPT_ENCODING";s:13:"gzip, deflate";s:20:"HTTP_ACCEPT_LANGUAGE";s:65:"en-RU,en;q=0.9,ru-RU;q=0.8,ru;q=0.7,en-US;q=0.6,uz;q=0.5,hr;q=0.4";s:11:"HTTP_COOKIE";s:539:"language=4e1a22a706b2b67a2ac68d2d85cb839925d09aed085ca214408c5498b87a9ea2a%3A2%3A%7Bi%3A0%3Bs%3A8%3A%22language%22%3Bi%3A1%3Bs%3A2%3A%22uz%22%3B%7D; PHPSESSID=tg16mblupj6u875l5vqjrkigi7pqr8f3; _identity=d3bbf1a40fbb07a5e71f4b657b0d9caaa7c20725eb7f86d2213424408f95f8caa%3A2%3A%7Bi%3A0%3Bs%3A9%3A%22_identity%22%3Bi%3A1%3Bs%3A16%3A%22%5B1%2Cnull%2C2592000%5D%22%3B%7D; _csrf=5de5c8623f8b1f477dee3e2ed0014819171dc78456feef3a897ed3d5ed083c16a%3A2%3A%7Bi%3A0%3Bs%3A5%3A%22_csrf%22%3Bi%3A1%3Bs%3A32%3A%22q6T-dtfJoJoonDmVVkMNyEhTWI1Kz4Cz%22%3B%7D";s:4:"PATH";s:343:"d:\ospanel\modules\php\PHP_8.1\ext;d:\ospanel\modules\php\PHP_8.1\pear;d:\ospanel\modules\php\PHP_8.1\pear\bin;d:\ospanel\modules\php\PHP_8.1;d:\ospanel\modules\wget\bin;d:\ospanel\modules\http\Apache_2.4-PHP_8.0-8.1\bin;d:\ospanel\modules\http\Apache_2.4-PHP_8.0-8.1;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\system32\Wbem;C:\WINDOWS\SysWOW64";s:10:"SystemRoot";s:10:"C:\WINDOWS";s:7:"COMSPEC";s:27:"C:\WINDOWS\system32\cmd.exe";s:7:"PATHEXT";s:62:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW";s:6:"WINDIR";s:10:"C:\WINDOWS";s:16:"SERVER_SIGNATURE";s:0:"";s:15:"SERVER_SOFTWARE";s:6:"Apache";s:11:"SERVER_NAME";s:6:"silver";s:11:"SERVER_ADDR";s:9:"127.0.0.1";s:11:"SERVER_PORT";s:2:"80";s:11:"REMOTE_ADDR";s:9:"127.0.0.1";s:13:"DOCUMENT_ROOT";s:34:"D:/OSPanel/domains/silverzavod/web";s:14:"REQUEST_SCHEME";s:4:"http";s:14:"CONTEXT_PREFIX";s:0:"";s:21:"CONTEXT_DOCUMENT_ROOT";s:34:"D:/OSPanel/domains/silverzavod/web";s:12:"SERVER_ADMIN";s:18:"[no address given]";s:15:"SCRIPT_FILENAME";s:44:"D:/OSPanel/domains/silverzavod/web/index.php";s:11:"REMOTE_PORT";s:5:"50108";s:12:"REDIRECT_URL";s:23:"/backend/expenses/index";s:17:"GATEWAY_INTERFACE";s:7:"CGI/1.1";s:15:"SERVER_PROTOCOL";s:8:"HTTP/1.1";s:14:"REQUEST_METHOD";s:3:"GET";s:12:"QUERY_STRING";s:0:"";s:11:"REQUEST_URI";s:23:"/backend/expenses/index";s:11:"SCRIPT_NAME";s:10:"/index.php";s:8:"PHP_SELF";s:10:"/index.php";s:18:"REQUEST_TIME_FLOAT";d:1752318563.171526;s:12:"REQUEST_TIME";i:1752318563;}s:3:"GET";a:0:{}s:4:"POST";a:0:{}s:6:"COOKIE";a:4:{s:8:"language";s:102:"4e1a22a706b2b67a2ac68d2d85cb839925d09aed085ca214408c5498b87a9ea2a:2:{i:0;s:8:"language";i:1;s:2:"uz";}";s:9:"PHPSESSID";s:32:"tg16mblupj6u875l5vqjrkigi7pqr8f3";s:9:"_identity";s:118:"d3bbf1a40fbb07a5e71f4b657b0d9caaa7c20725eb7f86d2213424408f95f8caa:2:{i:0;s:9:"_identity";i:1;s:16:"[1,null,2592000]";}";s:5:"_csrf";s:130:"5de5c8623f8b1f477dee3e2ed0014819171dc78456feef3a897ed3d5ed083c16a:2:{i:0;s:5:"_csrf";i:1;s:32:"q6T-dtfJoJoonDmVVkMNyEhTWI1Kz4Cz";}";}s:5:"FILES";a:0:{}s:7:"SESSION";a:6:{s:7:"__flash";a:0:{}s:4:"__id";i:1;s:9:"__authKey";N;s:8:"__expire";i:1752325763;s:13:"last_activity";i:1752318563;s:7:"timeout";i:1800;}}";s:4:"user";s:2148:"a:5:{s:2:"id";i:1;s:8:"identity";a:8:{s:2:"id";s:1:"1";s:8:"username";s:7:"'admin'";s:9:"full_name";s:15:"'Administrator'";s:4:"role";s:1:"1";s:12:"access_token";s:42:"'Am4PA_XsYEchREdnLU19DkEKnev9Tqn03HNbrXNd'";s:8:"password";s:62:"'$2y$13$0eRlRvvyoXFPe1EZQr1a1OJtiuhoGEzSLGqLV0XWtF3ZVbwKTSkjO'";s:10:"created_at";s:21:"'2025-02-25 12:53:18'";s:10:"deleted_at";s:4:"null";}s:10:"attributes";a:8:{i:0;a:2:{s:9:"attribute";s:2:"id";s:5:"label";s:2:"ID";}i:1;a:2:{s:9:"attribute";s:8:"username";s:5:"label";s:10:"Логин";}i:2;a:2:{s:9:"attribute";s:9:"full_name";s:5:"label";s:6:"ФИО";}i:3;a:2:{s:9:"attribute";s:4:"role";s:5:"label";s:4:"Role";}i:4;a:2:{s:9:"attribute";s:12:"access_token";s:5:"label";s:12:"Access Token";}i:5;a:2:{s:9:"attribute";s:8:"password";s:5:"label";s:12:"Пароль";}i:6;a:2:{s:9:"attribute";s:10:"created_at";s:5:"label";s:25:"Дата создания";}i:7;a:2:{s:9:"attribute";s:10:"deleted_at";s:5:"label";s:25:"Дата удаления";}}s:13:"rolesProvider";O:26:"yii\data\ArrayDataProvider":12:{s:27:" yii\base\Component _events";a:0:{}s:35:" yii\base\Component _eventWildcards";a:0:{}s:30:" yii\base\Component _behaviors";N;s:2:"id";N;s:32:" yii\data\BaseDataProvider _sort";N;s:38:" yii\data\BaseDataProvider _pagination";N;s:32:" yii\data\BaseDataProvider _keys";N;s:34:" yii\data\BaseDataProvider _models";N;s:38:" yii\data\BaseDataProvider _totalCount";N;s:3:"key";N;s:9:"allModels";a:1:{s:5:"admin";a:7:{s:4:"type";i:1;s:4:"name";s:5:"admin";s:11:"description";s:13:"Administrator";s:8:"ruleName";N;s:4:"data";s:4:"null";s:9:"createdAt";i:**********;s:9:"updatedAt";i:**********;}}s:10:"modelClass";N;}s:19:"permissionsProvider";O:26:"yii\data\ArrayDataProvider":12:{s:27:" yii\base\Component _events";a:0:{}s:35:" yii\base\Component _eventWildcards";a:0:{}s:30:" yii\base\Component _behaviors";N;s:2:"id";s:4:"dp-1";s:32:" yii\data\BaseDataProvider _sort";N;s:38:" yii\data\BaseDataProvider _pagination";N;s:32:" yii\data\BaseDataProvider _keys";N;s:34:" yii\data\BaseDataProvider _models";N;s:38:" yii\data\BaseDataProvider _totalCount";N;s:3:"key";N;s:9:"allModels";a:0:{}s:10:"modelClass";N;}}";s:5:"asset";s:4902:"a:8:{s:26:"app\assets\DataTablesAsset";a:9:{s:10:"sourcePath";N;s:8:"basePath";s:34:"D:/OSPanel/domains/silverzavod/web";s:7:"baseUrl";s:0:"";s:7:"depends";a:3:{i:0;s:19:"yii\web\JqueryAsset";i:1;s:29:"yii\bootstrap4\BootstrapAsset";i:2;s:35:"yii\bootstrap4\BootstrapPluginAsset";}s:2:"js";a:3:{i:0;s:73:"https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.7/umd/popper.min.js";i:1;s:61:"https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js";i:2;s:65:"https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap4.min.js";}s:3:"css";a:1:{i:0;s:67:"https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap4.min.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:19:"yii\web\JqueryAsset";a:9:{s:10:"sourcePath";s:61:"D:\OSPanel\domains\silverzavod\vendor/bower-asset/jquery/dist";s:8:"basePath";s:50:"D:\OSPanel\domains\silverzavod\web\assets\e0ff1908";s:7:"baseUrl";s:16:"/assets/e0ff1908";s:7:"depends";a:0:{}s:2:"js";a:1:{i:0;s:9:"jquery.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:29:"yii\bootstrap4\BootstrapAsset";a:9:{s:10:"sourcePath";s:62:"D:\OSPanel\domains\silverzavod\vendor/npm-asset/bootstrap/dist";s:8:"basePath";s:50:"D:\OSPanel\domains\silverzavod\web\assets\c3f9d53c";s:7:"baseUrl";s:16:"/assets/c3f9d53c";s:7:"depends";a:0:{}s:2:"js";a:0:{}s:3:"css";a:1:{i:0;s:17:"css/bootstrap.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:35:"yii\bootstrap4\BootstrapPluginAsset";a:9:{s:10:"sourcePath";s:62:"D:\OSPanel\domains\silverzavod\vendor/npm-asset/bootstrap/dist";s:8:"basePath";s:50:"D:\OSPanel\domains\silverzavod\web\assets\c3f9d53c";s:7:"baseUrl";s:16:"/assets/c3f9d53c";s:7:"depends";a:2:{i:0;s:19:"yii\web\JqueryAsset";i:1;s:29:"yii\bootstrap4\BootstrapAsset";}s:2:"js";a:1:{i:0;s:22:"js/bootstrap.bundle.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:23:"app\assets\Select2Asset";a:9:{s:10:"sourcePath";N;s:8:"basePath";s:34:"D:/OSPanel/domains/silverzavod/web";s:7:"baseUrl";s:0:"";s:7:"depends";a:1:{i:0;s:19:"yii\web\JqueryAsset";}s:2:"js";a:1:{i:0;s:70:"https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js";}s:3:"css";a:1:{i:0;s:72:"https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:21:"yii\widgets\PjaxAsset";a:9:{s:10:"sourcePath";s:59:"D:\OSPanel\domains\silverzavod\vendor/bower-asset/yii2-pjax";s:8:"basePath";s:50:"D:\OSPanel\domains\silverzavod\web\assets\99d1aff7";s:7:"baseUrl";s:16:"/assets/99d1aff7";s:7:"depends";a:1:{i:0;s:16:"yii\web\YiiAsset";}s:2:"js";a:1:{i:0;s:14:"jquery.pjax.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:16:"yii\web\YiiAsset";a:9:{s:10:"sourcePath";s:57:"D:\OSPanel\domains\silverzavod\vendor\yiisoft\yii2/assets";s:8:"basePath";s:50:"D:\OSPanel\domains\silverzavod\web\assets\e0686f09";s:7:"baseUrl";s:16:"/assets/e0686f09";s:7:"depends";a:1:{i:0;s:19:"yii\web\JqueryAsset";}s:2:"js";a:1:{i:0;s:6:"yii.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:19:"app\assets\AppAsset";a:9:{s:10:"sourcePath";N;s:8:"basePath";s:34:"D:/OSPanel/domains/silverzavod/web";s:7:"baseUrl";s:0:"";s:7:"depends";a:2:{i:0;s:16:"yii\web\YiiAsset";i:1;s:19:"yii\web\JqueryAsset";}s:2:"js";a:18:{i:0;s:37:"modules/bootstrap/js/bootstrap.min.js";i:1;s:35:"modules/bootstrap-datepicker.min.js";i:2;s:43:"modules/nicescroll/jquery.nicescroll.min.js";i:3;s:21:"modules/moment.min.js";i:4;s:52:"modules/bootstrap-daterangepicker/daterangepicker.js";i:5;s:12:"js/stisla.js";i:6;s:35:"modules/izitoast/js/iziToast.min.js";i:7;s:38:"modules/select2/dist/js/select2.min.js";i:8;s:34:"modules/jquery-ui/jquery-ui.min.js";i:9;s:13:"js/scripts.js";i:10;s:26:"js/jquery.inputmask.min.js";i:11;s:47:"modules/chocolat/dist/js/jquery.chocolat.min.js";i:12;s:27:"js/jquery.magnific-popup.js";i:13;s:10:"js/menu.js";i:14;s:21:"modules/moment.min.js";i:15;s:59:"modules/bootstrap-timepicker/js/bootstrap-timepicker.min.js";i:16;s:64:"//cdnjs.cloudflare.com/ajax/libs/numeral.js/2.0.6/numeral.min.js";i:17;s:21:"js/input-formatter.js";}s:3:"css";a:12:{i:0;s:12:"css/site.css";i:1;s:39:"modules/bootstrap/css/bootstrap.min.css";i:2;s:53:"modules/bootstrap-daterangepicker/daterangepicker.css";i:3;s:36:"modules/bootstrap-datepicker.min.css";i:4;s:35:"modules/fontawesome/css/all.min.css";i:5;s:37:"modules/ionicons/css/ionicons.min.css";i:6;s:40:"modules/select2/dist/css/select2.min.css";i:7;s:37:"modules/izitoast/css/iziToast.min.css";i:8;s:13:"css/style.css";i:9;s:18:"css/components.css";i:10;s:22:"css/magnific-popup.css";i:11;s:61:"modules/bootstrap-timepicker/css/bootstrap-timepicker.min.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}}";s:7:"summary";a:13:{s:3:"tag";s:13:"68724263517ac";s:3:"url";s:36:"http://silver/backend/expenses/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752318563.171526;s:10:"statusCode";i:200;s:8:"sqlCount";i:46;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10677576;s:14:"processingTime";d:0.22613286972045898;}s:10:"exceptions";a:0:{}}