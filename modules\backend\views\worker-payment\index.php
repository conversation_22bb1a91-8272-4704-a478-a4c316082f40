<?php

use yii\helpers\Html;
use yii\widgets\Pjax;
use app\assets\DataTablesAsset;

// Подключаем хелпер для работы с датами
require_once Yii::getAlias('@app/helpers/date-formatter.php');

DataTablesAsset::register($this);

$searchLabel = Yii::t("app", "search:");
$lengthMenuLabel = Yii::t("app", "Show _MENU_ entries");
$zeroRecordsLabel = Yii::t("app", "Nothing found");
$infoLabel = Yii::t("app", "Showing _PAGE_ to _PAGES_ of _MAX_ items");
$infoEmptyLabel = Yii::t("app", "Nothing found");
$infoFilteredLabel = Yii::t("app", "(filtered from _MAX_ records)");

$this->title = Yii::t('app', 'worker_payment_report');

?>

<style>
    /* Стили для полей ввода месяца */
    input[type="month"].form-control {
        width: 150px;
    }

    /* Отступы между элементами */
    .d-flex.gap-2 {
        gap: 0.5rem !important;
    }

    .text-right {
        text-align: right !important;
    }

    .text-center {
        text-align: center !important;
    }

    .table th {
        font-weight: bold;
        background-color: #f8f9fa;
    }

    .table td {
        vertical-align: middle;
    }

    .badge-success {
        background-color: #28a745;
        color: white;
    }

    .badge-warning {
        background-color: #ffc107;
        color: black;
    }

    .badge-danger {
        background-color: #dc3545;
        color: white;
    }

     .modal-dialog {
        max-width: 750px !important;
    }
</style>

<div class="card-body">
    <div class="row align-items-center mb-3">
        <div class="col-md-4">
            <h4 class="my-0"><?= Html::encode($this->title) ?></h4>
        </div>

        <div class="col-md-8">
            <div class="d-flex justify-content-end align-items-center gap-2">
                <!-- Month selector -->
                <div class="d-flex gap-2">
                    <input type="month" id="month_filter" class="form-control" value="<?= $month ?>" placeholder="<?= Yii::t('app', 'Select Month') ?>">
                </div>

                <!-- Search button -->
                <button type="button" class="btn btn-primary" id="search-button">
                    <?= Yii::t('app', 'search') ?>
                </button>

                <!-- Export button -->
                <button type="button" class="btn btn-success" id="export-button">
                    <i class="fas fa-file-excel"></i> <?= Yii::t('app', 'export_excel') ?>
                </button>

                <!-- Create Payment button -->
                <a href="#" class="btn btn-info worker-payment-create" data-toggle="modal" data-target="#ideal-mini-modal">
                    <i class="fas fa-plus"></i> <?= Yii::t('app', 'create_payment') ?>
                </a>
            </div>
        </div>
    </div>

    <!-- Month display -->
    <div class="row mb-3">
        <div class="col-md-12">
            <div>
                <strong><?= Yii::t('app', 'Report for month') ?>:</strong> <?= $monthName ?>
            </div>
        </div>
    </div>

    <?php Pjax::begin(['id' => 'worker-payment-grid-pjax']); ?>
    <?php if($result): ?>
        <div>
            <table id="worker-payment-grid-view" class="table table-bordered table-striped compact">
                <thead>
                    <tr>
                        <th><?= Yii::t("app", "full_name") ?></th>
                        <th><?= Yii::t("app", "position") ?></th>
                        <th><?= Yii::t("app", "salary") ?></th>
                        <th><?= Yii::t("app", "bonus") ?></th>
                        <th><?= Yii::t("app", "vacation_pay") ?></th>
                        <th><?= Yii::t("app", "advance") ?></th>
                        <th><?= Yii::t("app", "card_payment") ?></th>
                        <th><?= Yii::t("app", "debt") ?></th>
                        <th><?= Yii::t("app", "debt_deduction") ?></th>
                        <th><?= Yii::t("app", "received_amount") ?></th>
                        <th><?= Yii::t("app", "actions") ?></th>
                    </tr>
                </thead>
                <tbody>
                <?php 
                $totalSalary = 0;
                $totalBonus = 0;
                $totalVacationPay = 0;
                $totalAdvance = 0;
                $totalCardPayment = 0;
                $totalDebt = 0;
                $totalDebtDeduction = 0;
                $totalReceived = 0;
                ?>
                <?php foreach ($result as $model): ?>
                    <?php
                    $totalSalary += $model['salary'] ?? 0;
                    $totalBonus += ($model['bonus'] ?? 0) + ($model['cash_bonus'] ?? 0) + ($model['vacation_pay'] ?? 0) + ($model['cash_vacation_pay'] ?? 0) + ($model['one_time_payment'] ?? 0) + ($model['cash_one_time_payment'] ?? 0);
                    $totalVacationPay += ($model['vacation_pay'] ?? 0) + ($model['cash_vacation_pay'] ?? 0);
                    $totalAdvance += ($model['advance'] ?? 0) + ($model['cash_advance'] ?? 0) + ($model['cash_payment'] ?? 0);
                    $totalCardPayment += ($model['card_payment'] ?? 0) + ($model['bonus'] ?? 0) + ($model['vacation_pay'] ?? 0) + ($model['advance'] ?? 0) + ($model['one_time_payment'] ?? 0);
                    $totalDebt += $model['debt'] ?? 0;
                    $totalDebtDeduction += $model['debt_deduction'] ?? 0;
                    $totalReceived += $model['received_amount'] ?? 0;
                    ?>
                    <tr>
                        <td><?= Html::encode($model['full_name']) ?></td>
                        <td><?= Html::encode($model['position']) ?></td>
                        <td class="text-right"><?= number_format($model['salary'] ?? 0, 0, ',', ' ') ?></td>
                        <td class="text-right">
                            <?php
                            $totalBonusAndOther = ($model['bonus'] ?? 0) + ($model['cash_bonus'] ?? 0) + ($model['vacation_pay'] ?? 0) + ($model['cash_vacation_pay'] ?? 0) + ($model['one_time_payment'] ?? 0) + ($model['cash_one_time_payment'] ?? 0);
                            if($totalBonusAndOther > 0): ?>
                                <span class="badge badge-success"><?= number_format($totalBonusAndOther, 0, ',', ' ') ?></span>
                            <?php else: ?>
                                <span class="text-muted">0</span>
                            <?php endif; ?>
                        </td>
                        <td class="text-right">
                            <?php
                            $totalVacationPay = ($model['vacation_pay'] ?? 0) + ($model['cash_vacation_pay'] ?? 0);
                            if($totalVacationPay > 0): ?>
                                <span class="badge badge-success"><?= number_format($totalVacationPay, 0, ',', ' ') ?></span>
                            <?php else: ?>
                                <span class="text-muted">0</span>
                            <?php endif; ?>
                        </td>
                        <td class="text-right">
                            <?php
                            $totalAdvanceWithCash = ($model['advance'] ?? 0) + ($model['cash_advance'] ?? 0) + ($model['cash_payment'] ?? 0);
                            if($totalAdvanceWithCash > 0): ?>
                                <span class="badge badge-warning"><?= number_format($totalAdvanceWithCash, 0, ',', ' ') ?></span>
                            <?php else: ?>
                                <span class="text-muted">0</span>
                            <?php endif; ?>
                        </td>
                        <td class="text-right">
                            <?php
                            $totalCardPayment = ($model['card_payment'] ?? 0) + ($model['bonus'] ?? 0) + ($model['vacation_pay'] ?? 0) + ($model['advance'] ?? 0) + ($model['one_time_payment'] ?? 0);
                            if($totalCardPayment > 0): ?>
                                <span class="badge badge-success"><?= number_format($totalCardPayment, 0, ',', ' ') ?></span>
                            <?php else: ?>
                                <span class="text-muted">0</span>
                            <?php endif; ?>
                        </td>
                        <td class="text-right">
                            <?php if($model['debt'] > 0): ?>
                                <span class="badge badge-danger"><?= number_format($model['debt'], 0, ',', ' ') ?></span>
                            <?php else: ?>
                                <span class="text-muted">0</span>
                            <?php endif; ?>
                        </td>
                        <td class="text-right">
                            <?php if($model['debt_deduction'] > 0): ?>
                                <span class="badge badge-warning"><?= number_format($model['debt_deduction'], 0, ',', ' ') ?></span>
                            <?php else: ?>
                                <span class="text-muted">0</span>
                            <?php endif; ?>
                        </td>
                        <td class="text-right">
                            <strong><?= number_format($model['received_amount'], 0, ',', ' ') ?></strong>
                        </td>
                        <td class="text-center">
                            <button type="button" 
                                class="btn btn-sm <?= $model['has_payments'] ? 'btn-primary' : 'btn-secondary' ?> worker-payment-edit" 
                                data-worker-id="<?= $model['worker_id'] ?>" 
                                data-month="<?= $month ?>"
                                data-toggle="modal" 
                                data-target="#ideal-mini-modal"
                                <?= !$model['has_payments'] ? 'disabled' : '' ?>
                                title="<?= $model['has_payments'] ? Yii::t('app', 'edit_payment') : Yii::t('app', 'no_payments_to_edit') ?>">
                                <i class="fas fa-edit"></i>
                            </button>
                        </td>
                    </tr>
                <?php endforeach; ?>
                </tbody>
                <tfoot>
                    <tr style="background-color: #f8f9fa; font-weight: bold;">
                        <td colspan="2" class="text-right"><?= Yii::t('app', 'Total') ?>:</td>
                        <td class="text-right"><?= number_format($totalSalary, 0, ',', ' ') ?></td>
                        <td class="text-right"><?= number_format($totalBonus, 0, ',', ' ') ?></td>
                        <td class="text-right"><?= number_format($totalVacationPay, 0, ',', ' ') ?></td>
                        <td class="text-right"><?= number_format($totalAdvance, 0, ',', ' ') ?></td>
                        <td class="text-right"><?= number_format($totalCardPayment, 0, ',', ' ') ?></td>
                        <td class="text-right"><?= number_format($totalDebt, 0, ',', ' ') ?></td>
                        <td class="text-right"><?= number_format($totalDebtDeduction, 0, ',', ' ') ?></td>
                        <td class="text-right"><strong><?= number_format($totalReceived, 0, ',', ' ') ?></strong></td>
                        <td class="text-center">-</td>
                    </tr>
                </tfoot>
            </table>
        </div>
    <?php else: ?>
        <div class="alert alert-warning">
            <?= Yii::t('app', 'no_data_available') ?>
        </div>
    <?php endif; ?>
    <?php Pjax::end(); ?>
</div>



<div id="create-payment-text" data-text="<?= Yii::t('app', 'create_payment') ?>"></div>
<div id="edit-payment-text" data-text="<?= Yii::t('app', 'edit_payment') ?>"></div>

<?php
$js = <<<JS
(function($) {
    // Функция инициализации DataTable
    function initializeDataTable() {
        if ($.fn.DataTable.isDataTable('#worker-payment-grid-view')) {
            $('#worker-payment-grid-view').DataTable().destroy();
        }
        
        $('#worker-payment-grid-view').DataTable({
            "language": {
                "search": "$searchLabel",
                "lengthMenu": "$lengthMenuLabel",
                "zeroRecords": "$zeroRecordsLabel",
                "info": "$infoLabel",
                "infoEmpty": "$infoEmptyLabel",
                "infoFiltered": "$infoFilteredLabel"
            },
            "pageLength": 25,
            "order": [[0, "asc"]],
            "responsive": true,
            "autoWidth": false,
            "columnDefs": [
                {
                    "targets": [2,3,4,5,6,7,8,9], // Numeric columns
                    "className": "text-right"
                },
                {
                    "targets": [10], // Actions column
                    "orderable": false,
                    "searchable": false,
                    "className": "text-center"
                }
            ]
        });
    }

    // Initialize DataTable при загрузке страницы
    initializeDataTable();

    // Search functionality
    $('#search-button').click(function() {
        var month = $('#month_filter').val();
        
        if (!month) {
            alert('Пожалуйста, выберите месяц');
            return;
        }
        
        $.ajax({
            url: '/backend/worker-payment/search',
            type: 'POST',
            data: {
                month: month
            },
            success: function(response) {
                if (response.status === 'success') {
                    // Update table with new data
                    updateTable(response.data, month);
                } else {
                    alert('Ошибка: ' + (response.message || 'Неизвестная ошибка'));
                }
            },
            error: function() {
                alert('Ошибка при загрузке данных');
            }
        });
    });

    // Export functionality
    $('#export-button').click(function() {
        var month = $('#month_filter').val() || '{$month}';
        window.location.href = '/backend/worker-payment/export?month=' + month;
    });

    var createPaymentText = $('#create-payment-text').attr('data-text');

    // Initialize Worker Payment Create functionality
    function initializeWorkerPaymentCreate() {
        $(document).off('click.worker-payment-create').on('click.worker-payment-create', '.worker-payment-create', function() {
            $.ajax({
                url: '/backend/worker-payment/create',
                dataType: 'json',
                type: 'GET',
                success: function(response) {
                    $('#ideal-mini-modal .modal-title').html(createPaymentText);
                    $('#ideal-mini-modal .modal-body').html(response.content);
                    $('#ideal-mini-modal .mini-button').addClass("worker-payment-submit-button");
                    $('#ideal-mini-modal').modal('show');
                    
                    // Инициализируем Select2
                    initializeSelect2();
                    
                    // Инициализируем все обработчики для формы платежа
                    initializePaymentFormHandlers();
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        // Обработка успешного создания платежа из модалки
        $(document).on('paymentCreated', function() {
            $('.close').trigger('click');
            $.pjax.reload({
                container: '#worker-payment-grid-pjax',
                timeout: false
            });
        });
    }

    // Initialize Worker Payment Edit functionality
    function initializeWorkerPaymentEdit() {
        $(document).off('click.worker-payment-edit').on('click.worker-payment-edit', '.worker-payment-edit', function() {
            var workerId = $(this).data('worker-id');
            var month = $(this).data('month');
            var editPaymentText = $('#edit-payment-text').attr('data-text');

            $.ajax({
                url: '/backend/worker-payment/edit',
                dataType: 'json',
                type: 'GET',
                data: {
                    worker_id: workerId,
                    month: month
                },
                success: function(response) {
                    // Очищаем модальное окно перед загрузкой нового содержимого
                    $('#ideal-mini-modal .modal-body').empty();
                    $('#ideal-mini-modal .mini-button').removeClass("worker-payment-update-button").prop('disabled', false);

                    // Загружаем новое содержимое
                    $('#ideal-mini-modal .modal-title').html(editPaymentText);
                    $('#ideal-mini-modal .modal-body').html(response.content);
                    $('#ideal-mini-modal .mini-button').addClass("worker-payment-update-button");
                    $('#ideal-mini-modal').modal('show');
                    
                    // ОТКЛЮЧЕНО: Загрузка старых JS файлов для редактирования
                    // Теперь весь JavaScript находится прямо в edit.php
                    // loadEditJsFiles().then(function() {
                        // Инициализируем Select2
                        initializeSelect2();

                        // ОТКЛЮЧЕНО: Инициализация старых обработчиков
                        // initializePaymentFormHandlers();
                    // });
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                    alert('Ошибка при загрузке формы редактирования');
                }
            });
        });

        // Обработка успешного обновления платежа из модалки
        $(document).on('paymentUpdated', function() {
            $('.close').trigger('click');
            $.pjax.reload({
                container: '#worker-payment-grid-pjax',
                timeout: false
            });
        });
    }

    // Функция для загрузки JS файлов редактирования
    function loadEditJsFiles() {
        return new Promise(function(resolve) {
            // Проверяем, загружены ли уже файлы
            if (typeof window.WorkerPaymentEditHandlers !== 'undefined') {
                resolve();
                return;
            }

            var jsFiles = [
                '/js/worker-payment/edit-calculations.js',
                '/js/worker-payment/edit-ui-helpers.js',
                '/js/worker-payment/edit-validation.js',
                '/js/worker-payment/edit-ajax-requests.js',
                '/js/worker-payment/edit-form-handlers.js',
                '/js/worker-payment/edit-main.js'
            ];

            var loadedCount = 0;
            var totalFiles = jsFiles.length;

            jsFiles.forEach(function(src) {
                var script = document.createElement('script');
                script.src = src;
                script.onload = function() {
                    loadedCount++;
                    if (loadedCount === totalFiles) {
                        resolve();
                    }
                };
                script.onerror = function() {
                    console.error('Ошибка загрузки скрипта:', src);
                    loadedCount++;
                    if (loadedCount === totalFiles) {
                        resolve();
                    }
                };
                document.head.appendChild(script);
            });
        });
    }

    function initializeSelect2() {
        $('.select2').each(function() {
            var parentModal = $(this).closest('.modal');
            $(this).select2({
                width: '100%',
                language: {
                    noResults: function() {
                        return "<?= Yii::t('app', 'No results found') ?>";
                    },
                    searching: function() {
                        return "<?= Yii::t('app', 'Searching...') ?>";
                    }
                },
                allowClear: true,
                placeholder: function() {
                    return $(this).find('option:first').text();
                },
                dropdownParent: parentModal.length ? parentModal : $(document.body)
            });
        });
    }

    function updateTable(data, month) {
        
        // Полностью уничтожаем DataTable
        if ($.fn.DataTable.isDataTable('#worker-payment-grid-view')) {
            $('#worker-payment-grid-view').DataTable().destroy();
        }
        
        var tableBody = $('#worker-payment-grid-view tbody');
        var tableFoot = $('#worker-payment-grid-view tfoot');
        
        // Полностью очищаем содержимое
        tableBody.empty();
        
        // Проверяем, есть ли данные
        if (!data || data.length === 0) {
            tableBody.append('<tr><td colspan="11" class="text-center">Нет данных за выбранный месяц</td></tr>');
            tableFoot.hide();
            initializeDataTable();
            return;
        }
        
        tableFoot.show();
        
        var totalSalary = 0, totalBonus = 0, totalVacationPay = 0, totalAdvance = 0;
        var totalCardPayment = 0, totalDebt = 0, totalDebtDeduction = 0, totalReceived = 0;
        
        $.each(data, function(index, row) {


            totalSalary += parseFloat(row.salary || 0);
            totalBonus += parseFloat(row.bonus || 0) + parseFloat(row.cash_bonus || 0) + parseFloat(row.vacation_pay || 0) + parseFloat(row.cash_vacation_pay || 0) + parseFloat(row.one_time_payment || 0) + parseFloat(row.cash_one_time_payment || 0);
            totalVacationPay += parseFloat(row.vacation_pay || 0) + parseFloat(row.cash_vacation_pay || 0);
            totalAdvance += parseFloat(row.advance || 0) + parseFloat(row.cash_advance || 0) + parseFloat(row.cash_payment || 0);
            totalCardPayment += parseFloat(row.card_payment || 0) + parseFloat(row.bonus || 0) + parseFloat(row.vacation_pay || 0) + parseFloat(row.advance || 0) + parseFloat(row.one_time_payment || 0);
            totalDebt += parseFloat(row.debt || 0);
            totalDebtDeduction += parseFloat(row.debt_deduction || 0);
            totalReceived += parseFloat(row.received_amount || 0);

            var totalBonusAndOther = parseFloat(row.bonus || 0) + parseFloat(row.cash_bonus || 0) + parseFloat(row.vacation_pay || 0) + parseFloat(row.cash_vacation_pay || 0) + parseFloat(row.one_time_payment || 0) + parseFloat(row.cash_one_time_payment || 0);
            var totalVacationPayRow = parseFloat(row.vacation_pay || 0) + parseFloat(row.cash_vacation_pay || 0);
            var totalAdvanceWithCash = parseFloat(row.advance || 0) + parseFloat(row.cash_advance || 0) + parseFloat(row.cash_payment || 0);
            var totalCardPaymentRow = parseFloat(row.card_payment || 0) + parseFloat(row.bonus || 0) + parseFloat(row.vacation_pay || 0) + parseFloat(row.advance || 0) + parseFloat(row.one_time_payment || 0);

            var tr = $('<tr>');
            tr.append($('<td>').text(row.full_name));
            tr.append($('<td>').text(row.position));
            tr.append($('<td class="text-right">').text(formatNumber(row.salary || 0)));
            tr.append($('<td class="text-right">').html(formatBadge(totalBonusAndOther, 'success')));
            tr.append($('<td class="text-right">').html(formatBadge(totalVacationPayRow, 'success')));
            tr.append($('<td class="text-right">').html(formatBadge(totalAdvanceWithCash, 'warning')));
            tr.append($('<td class="text-right">').html(formatBadge(totalCardPaymentRow, 'success')));
            tr.append($('<td class="text-right">').html(formatBadge(row.debt, 'danger')));
            tr.append($('<td class="text-right">').html(formatBadge(row.debt_deduction, 'warning')));
            tr.append($('<td class="text-right">').html('<strong>' + formatNumber(row.received_amount || 0) + '</strong>'));
            tr.append($('<td class="text-center">').html('<button type="button" class="btn btn-sm btn-primary worker-payment-edit" data-worker-id="' + row.worker_id + '" data-month="' + month + '" data-toggle="modal" data-target="#ideal-mini-modal" title="Редактировать"><i class="fas fa-edit"></i></button>'));

            tableBody.append(tr);
        });
        
        // Update totals
        var tfoot = $('#worker-payment-grid-view tfoot tr');
        tfoot.find('td:eq(2)').text(formatNumber(totalSalary));
        tfoot.find('td:eq(3)').text(formatNumber(totalBonus));
        tfoot.find('td:eq(4)').text(formatNumber(totalVacationPay));
        tfoot.find('td:eq(5)').text(formatNumber(totalAdvance));
        tfoot.find('td:eq(6)').text(formatNumber(totalCardPayment));
        tfoot.find('td:eq(7)').text(formatNumber(totalDebt));
        tfoot.find('td:eq(8)').text(formatNumber(totalDebtDeduction));
        tfoot.find('td:eq(9)').html('<strong>' + formatNumber(totalReceived) + '</strong>');
        tfoot.find('td:eq(10)').html('-');
        
        // Update month display
        var monthDisplay = new Date(month + '-01').toLocaleDateString('ru-RU', {
            year: 'numeric',
            month: 'long'
        });
        $('.alert-info').html('<strong>Отчет за месяц:</strong> ' + monthDisplay);
        
        // Принудительно пересоздаем DataTable
        setTimeout(function() {
            initializeDataTable();
        }, 100);
    }

    function formatNumber(num) {
        return parseFloat(num || 0).toLocaleString('ru-RU');
    }

    function formatBadge(value, type) {
        var num = parseFloat(value || 0);
        if (num > 0) {
            return '<span class="badge badge-' + type + '">' + formatNumber(num) + '</span>';
        } else {
            return '<span class="text-muted">0</span>';
        }
    }

    /**
     * Инициализация обработчиков формы платежа после загрузки в модалку
     */
    function initializePaymentFormHandlers() {
        
        // Проверяем, какая форма загружена
        if ($('#worker-payment-edit-form').length > 0) {
            // Инициализируем обработчики для редактирования
            if (typeof window.WorkerPaymentEditHandlers !== 'undefined') {
                window.WorkerPaymentEditHandlers.init();
            } else {
                console.warn('WorkerPaymentEditHandlers not found');
            }
            
            // Инициализируем приложение редактирования
            if (typeof window.WorkerPaymentEditApp !== 'undefined') {
                window.WorkerPaymentEditApp.init();
            }
        } else if ($('#worker-payment-create-form').length > 0) {
            // Инициализируем обработчики для создания
            if (typeof window.WorkerPaymentHandlers !== 'undefined') {
                window.WorkerPaymentHandlers.init();
            } else {
                console.warn('WorkerPaymentHandlers not found');
            }
        }
    }

    // Обработчик отправки формы перенесен в модульную систему (form-handlers.js)

    function initializeAll() {
        initializeWorkerPaymentCreate();
        initializeWorkerPaymentEdit();
        initializeSelect2();
        initializeDataTable();
    }
    
    $(document).ready(initializeAll);
    $(document).on('pjax:complete', initializeAll);

})(jQuery);
JS;

$this->registerJs($js);

// Подключаем JavaScript модули для worker-payment
$this->registerJsFile(Yii::getAlias('@web') . '/js/worker-payment/validation.js', ['depends' => [\yii\web\JqueryAsset::class]]);
$this->registerJsFile(Yii::getAlias('@web') . '/js/worker-payment/calculations.js', ['depends' => [\yii\web\JqueryAsset::class]]);
$this->registerJsFile(Yii::getAlias('@web') . '/js/worker-payment/ui-helpers.js', ['depends' => [\yii\web\JqueryAsset::class]]);
$this->registerJsFile(Yii::getAlias('@web') . '/js/worker-payment/ajax-requests.js', ['depends' => [\yii\web\JqueryAsset::class]]);
$this->registerJsFile(Yii::getAlias('@web') . '/js/worker-payment/form-handlers.js', ['depends' => [\yii\web\JqueryAsset::class]]);
$this->registerJsFile(Yii::getAlias('@web') . '/js/worker-payment/main.js', ['depends' => [\yii\web\JqueryAsset::class]]);

// Инициализация констант для JavaScript (только при первой загрузке страницы)
$constantsJs = <<<JS
// Инициализация констант Worker Payment App (только если еще не объявлены)
if (typeof WorkerPaymentApp !== 'undefined' && typeof WorkerPaymentApp.constants === 'undefined') {
    WorkerPaymentApp.ready({
        WORKER_FINANCES_TYPE_SALARY: 1,
        WORKER_FINANCES_TYPE_ADVANCE: 2,
        WORKER_FINANCES_TYPE_BONUS: 3,
        WORKER_FINANCES_TYPE_ONE_TIME_PAYMENT: 5,
        WORKER_FINANCES_TYPE_DEBT_PAYMENT: 6,
        WORKER_FINANCES_TYPE_VACATION_PAY: 7,
        WORKER_FINANCES_TYPE_CASH_SALARY: 8,
        WORKER_FINANCES_TYPE_CASH_ADVANCE: 9,
        WORKER_FINANCES_TYPE_CASH_BONUS: 10,
        WORKER_FINANCES_TYPE_CASH_ONE_TIME_PAYMENT: 11,
        WORKER_FINANCES_TYPE_CASH_VACATION_PAY: 12,
        PAYMENT_TYPE_CASH: 1,
        PAYMENT_TYPE_TRANSFER: 2,
        PAYMENT_TYPE_TERMINAL: 3,
        PAYMENT_TYPE_PAYMENT_CARD: 4,
        CASH_LABEL: "Наличные",
        PAYMENT_CARD_LABEL: "Платежная карта"
    });
} else if (typeof WorkerPaymentApp === 'undefined') {
    // Если WorkerPaymentApp не существует, создаем глобальные константы
    if (typeof window.WORKER_FINANCES_TYPE_SALARY === 'undefined') {
        window.WORKER_FINANCES_TYPE_SALARY = 1;
        window.WORKER_FINANCES_TYPE_ADVANCE = 2;
        window.WORKER_FINANCES_TYPE_BONUS = 3;
        window.WORKER_FINANCES_TYPE_ONE_TIME_PAYMENT = 5;
        window.WORKER_FINANCES_TYPE_DEBT_PAYMENT = 6;
        window.WORKER_FINANCES_TYPE_VACATION_PAY = 7;
        window.WORKER_FINANCES_TYPE_CASH_SALARY = 8;
        window.WORKER_FINANCES_TYPE_CASH_ADVANCE = 9;
        window.WORKER_FINANCES_TYPE_CASH_BONUS = 10;
        window.WORKER_FINANCES_TYPE_CASH_ONE_TIME_PAYMENT = 11;
        window.WORKER_FINANCES_TYPE_CASH_VACATION_PAY = 12;
        window.PAYMENT_TYPE_CASH = 1;
        window.PAYMENT_TYPE_TRANSFER = 2;
        window.PAYMENT_TYPE_TERMINAL = 3;
        window.PAYMENT_TYPE_PAYMENT_CARD = 4;
        window.CASH_LABEL = "Наличные";
        window.PAYMENT_CARD_LABEL = "Платежная карта";
    }
}
JS;

// Регистрируем константы только при первой загрузке страницы (не при PJAX)
$this->registerJs($constantsJs, \yii\web\View::POS_HEAD);
?>
