a:14:{s:6:"config";s:4971:"a:5:{s:10:"phpVersion";s:5:"8.1.9";s:10:"yiiVersion";s:10:"2.0.52-dev";s:11:"application";a:8:{s:3:"yii";s:10:"2.0.52-dev";s:4:"name";s:14:"My Application";s:7:"version";s:3:"1.0";s:8:"language";s:2:"uz";s:14:"sourceLanguage";s:5:"en-US";s:7:"charset";s:5:"UTF-8";s:3:"env";s:3:"dev";s:5:"debug";b:1;}s:3:"php";a:5:{s:7:"version";s:5:"8.1.9";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;s:9:"memcached";b:0;}s:10:"extensions";a:16:{s:25:"kartik-v/yii2-krajee-base";a:3:{s:4:"name";s:25:"kartik-v/yii2-krajee-base";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/base";s:67:"D:\OSPanel\domains\silverzavod\vendor/kartik-v/yii2-krajee-base/src";}}s:20:"kartik-v/yii2-dialog";a:3:{s:4:"name";s:20:"kartik-v/yii2-dialog";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:14:"@kartik/dialog";s:62:"D:\OSPanel\domains\silverzavod\vendor/kartik-v/yii2-dialog/src";}}s:18:"kartik-v/yii2-grid";a:3:{s:4:"name";s:18:"kartik-v/yii2-grid";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/grid";s:60:"D:\OSPanel\domains\silverzavod\vendor/kartik-v/yii2-grid/src";}}s:35:"kartik-v/yii2-widget-datetimepicker";a:3:{s:4:"name";s:35:"kartik-v/yii2-widget-datetimepicker";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:16:"@kartik/datetime";s:77:"D:\OSPanel\domains\silverzavod\vendor/kartik-v/yii2-widget-datetimepicker/src";}}s:16:"yiisoft/yii2-jui";a:3:{s:4:"name";s:16:"yiisoft/yii2-jui";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:8:"@yii/jui";s:58:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-jui/src";}}s:33:"2amigos/yii2-arrayquery-component";a:3:{s:4:"name";s:33:"2amigos/yii2-arrayquery-component";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:21:"@dosamigos/arrayquery";s:75:"D:\OSPanel\domains\silverzavod\vendor/2amigos/yii2-arrayquery-component/src";}}s:17:"yii2mod/yii2-rbac";a:3:{s:4:"name";s:17:"yii2mod/yii2-rbac";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:13:"@yii2mod/rbac";s:55:"D:\OSPanel\domains\silverzavod\vendor/yii2mod/yii2-rbac";}}s:23:"yiisoft/yii2-bootstrap5";a:4:{s:4:"name";s:23:"yiisoft/yii2-bootstrap5";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:15:"@yii/bootstrap5";s:65:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-bootstrap5/src";}s:9:"bootstrap";s:40:"yii\bootstrap5\i18n\TranslationBootstrap";}s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:8:"2.1.25.0";s:5:"alias";a:1:{s:10:"@yii/debug";s:60:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-debug/src";}}s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:10:"@yii/faker";s:60:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-faker/src";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.2.6.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:58:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-gii/src";}}s:18:"yiisoft/yii2-queue";a:3:{s:4:"name";s:18:"yiisoft/yii2-queue";s:7:"version";s:23:"3.0.9999999.9999999-dev";s:5:"alias";a:10:{s:10:"@yii/queue";s:60:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src";s:13:"@yii/queue/db";s:71:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/db";s:14:"@yii/queue/sqs";s:72:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/sqs";s:15:"@yii/queue/file";s:73:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/file";s:15:"@yii/queue/sync";s:73:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/sync";s:16:"@yii/queue/redis";s:74:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/redis";s:16:"@yii/queue/stomp";s:74:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/stomp";s:18:"@yii/queue/gearman";s:76:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/gearman";s:20:"@yii/queue/beanstalk";s:78:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/beanstalk";s:23:"@yii/queue/amqp_interop";s:81:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/amqp_interop";}}s:26:"yiisoft/yii2-symfonymailer";a:3:{s:4:"name";s:26:"yiisoft/yii2-symfonymailer";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:18:"@yii/symfonymailer";s:68:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-symfonymailer/src";}}s:23:"yiisoft/yii2-bootstrap4";a:3:{s:4:"name";s:23:"yiisoft/yii2-bootstrap4";s:7:"version";s:8:"2.0.11.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap4";s:65:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-bootstrap4/src";}}s:20:"yii2mod/yii2-swagger";a:3:{s:4:"name";s:20:"yii2mod/yii2-swagger";s:7:"version";s:7:"1.0.0.0";s:5:"alias";a:1:{s:16:"@yii2mod/swagger";s:58:"D:\OSPanel\domains\silverzavod\vendor/yii2mod/yii2-swagger";}}s:23:"yiisoft/yii2-httpclient";a:3:{s:4:"name";s:23:"yiisoft/yii2-httpclient";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:15:"@yii/httpclient";s:65:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-httpclient/src";}}}}";s:3:"log";s:57729:"a:1:{s:8:"messages";a:71:{i:0;a:6:{i:0;s:68:"Bootstrap with yii\bootstrap5\i18n\TranslationBootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752320928.707658;i:4;a:0:{}i:5;i:2639008;}i:1;a:6:{i:0;s:33:"Bootstrap with yii\log\Dispatcher";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752320928.711852;i:4;a:0:{}i:5;i:2756944;}i:2;a:6:{i:0;s:53:"Bootstrap with app\components\SessionTimeoutComponent";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752320928.714369;i:4;a:0:{}i:5;i:2798152;}i:3;a:6:{i:0;s:22:"Bootstrap with Closure";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752320928.714392;i:4;a:0:{}i:5;i:2798528;}i:4;a:6:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1752320928.750688;i:4;a:0:{}i:5;i:3943728;}i:5;a:6:{i:0;s:15:"Session started";i:1;i:4;i:2;s:21:"yii\web\Session::open";i:3;d:1752320928.767289;i:4;a:0:{}i:5;i:4754216;}i:6;a:6:{i:0;s:74:"Opening DB connection: pgsql:host=127.0.0.1;port=5432;dbname=silver_test_1";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:1752320928.789512;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6058864;}i:9;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752320928.852949;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6196288;}i:12;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752320928.891706;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6306608;}i:15;a:6:{i:0;s:63:"SELECT * FROM "users" WHERE ("id"=1) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752320928.902245;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6601376;}i:18;a:6:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752320928.918495;i:4;a:0:{}i:5;i:7291704;}i:19;a:6:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1752320928.923004;i:4;a:0:{}i:5;i:8075616;}i:20;a:6:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752320928.923455;i:4;a:0:{}i:5;i:8100448;}i:45;a:6:{i:0;s:41:"Route requested: 'backend/expenses/index'";i:1;i:8;i:2;s:34:"yii\web\Application::handleRequest";i:3;d:1752320928.924284;i:4;a:0:{}i:5;i:8154552;}i:46;a:6:{i:0;s:23:"Loading module: backend";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1752320928.924294;i:4;a:0:{}i:5;i:8156176;}i:47;a:6:{i:0;s:36:"Route to run: backend/expenses/index";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:1752320928.926959;i:4;a:0:{}i:5;i:8324552;}i:48;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='1'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752320928.933496;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8805048;}i:51;a:6:{i:0;s:46:"SELECT * FROM "auth_item" WHERE "name"='admin'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752320928.937926;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8811664;}i:54;a:6:{i:0;s:20:"Checking role: admin";i:1;i:8;i:2;s:40:"yii\rbac\DbManager::checkAccessRecursive";i:3;d:1752320928.942203;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8815040;}i:55;a:6:{i:0;s:81:"Running action: app\modules\backend\controllers\ExpensesController::actionIndex()";i:1;i:8;i:2;s:36:"yii\base\InlineAction::runWithParams";i:3;d:1752320928.942256;i:4;a:0:{}i:5;i:8814192;}i:56;a:6:{i:0;s:428:"SELECT "expenses".*, "expenses_type"."name" AS "expense_type_name", "users"."full_name" AS "added_by_user" FROM "expenses" LEFT JOIN "expenses_type" ON expenses.expense_type_id = expenses_type.id LEFT JOIN "users" ON expenses.add_user_id = users.id WHERE ("expenses"."deleted_at" IS NULL) AND ("expenses"."created_at" >= '2025-07-12 00:00:00') AND ("expenses"."created_at" <= '2025-07-12 23:59:59') ORDER BY "expenses"."id" DESC";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752320928.945129;i:4;a:1:{i:0;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:32;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8896104;}i:59;a:6:{i:0;s:2814:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'expenses'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752320928.951104;i:4;a:1:{i:0;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:32;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8921744;}i:62;a:6:{i:0;s:876:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='expenses'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752320928.95976;i:4;a:1:{i:0;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:32;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8932424;}i:65;a:6:{i:0;s:56:"SELECT * FROM "expenses_type" WHERE "deleted_at" IS NULL";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752320928.965685;i:4;a:1:{i:0;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:36;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8961392;}i:68;a:6:{i:0;s:2819:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'expenses_type'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752320928.966569;i:4;a:1:{i:0;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:36;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8982288;}i:71;a:6:{i:0;s:881:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='expenses_type'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752320928.97274;i:4;a:1:{i:0;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:36;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8989464;}i:74;a:6:{i:0;s:92:"Rendering view file: D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1752320928.983081;i:4;a:1:{i:0;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9136296;}i:75;a:6:{i:0;s:126:"
        SELECT balance 
        FROM cashbox 
        WHERE title = 'Оля' AND deleted_at IS NULL
        LIMIT 1
    ";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752320928.988231;i:4;a:2:{i:0;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:21;s:8:"function";s:8:"queryOne";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9391536;}i:78;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=153";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752320928.999928;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9877416;}i:81;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=153) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.002962;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9943464;}i:84;a:6:{i:0;s:2814:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'tracking'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.007292;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9952504;}i:87;a:6:{i:0;s:876:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='tracking'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.015049;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9963304;}i:90;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=152";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.01954;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9965192;}i:93;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=152) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.02088;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9977336;}i:96;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=151";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.022515;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9978984;}i:99;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=151) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.023865;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9991128;}i:102;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=150";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.025554;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9992776;}i:105;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=150) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.026607;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10004920;}i:108;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=149";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.028649;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10022952;}i:111;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=149) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.02978;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10035096;}i:114;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=148";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.031575;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10036744;}i:117;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=148) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.032735;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10048888;}i:120;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=147";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.03445;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10050536;}i:123;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=147) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.035584;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10062680;}i:126;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=146";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.037444;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10064328;}i:129;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=146) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.038589;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10080568;}i:132;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=103";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.040678;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10082216;}i:135;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=103) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.042301;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10094360;}i:138;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=102";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.045275;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10096008;}i:141;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=102) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.046524;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10108152;}i:144;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=89";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.048929;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10113896;}i:147;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=89) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.050191;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10126040;}i:150;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=88";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.052319;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10127688;}i:153;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=88) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.053477;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10139832;}i:156;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=85";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.055626;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10157864;}i:159;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=85) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.057229;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10170008;}i:162;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=84";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.05902;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10171656;}i:165;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=84) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.060394;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10183800;}i:168;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=83";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.062156;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10185448;}i:171;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=83) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.063501;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10197592;}i:174;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=82";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.065355;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10199240;}i:177;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=82) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.066394;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10211384;}i:180;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=81";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.068106;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10213032;}i:183;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=81) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.06913;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10225176;}i:186;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=80";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.070661;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10226824;}i:189;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=80) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.071901;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10238968;}i:192;a:6:{i:0;s:74:"Rendering view file: D:\OSPanel\domains\silverzavod\views\layouts\main.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1752320929.080964;i:4;a:1:{i:0;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10270936;}i:193;a:6:{i:0;s:74:"Rendering view file: D:\OSPanel\domains\silverzavod\views\layouts\menu.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1752320929.083098;i:4;a:2:{i:0;a:5:{s:4:"file";s:53:"D:\OSPanel\domains\silverzavod\views\layouts\main.php";s:4:"line";i:23;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10331144;}i:194;a:6:{i:0;s:89:"Rendering view file: D:\OSPanel\domains\silverzavod\views\layouts\partials/menu-admin.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1752320929.084293;i:4;a:3:{i:0;a:5:{s:4:"file";s:53:"D:\OSPanel\domains\silverzavod\views\layouts\menu.php";s:4:"line";i:54;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:53:"D:\OSPanel\domains\silverzavod\views\layouts\main.php";s:4:"line";i:23;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10370536;}i:195;a:6:{i:0;s:78:"Rendering view file: D:\OSPanel\domains\silverzavod\views\layouts\sub-menu.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1752320929.086826;i:4;a:2:{i:0;a:5:{s:4:"file";s:53:"D:\OSPanel\domains\silverzavod\views\layouts\main.php";s:4:"line";i:43;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10361776;}i:196;a:6:{i:0;s:75:"Rendering view file: D:\OSPanel\domains\silverzavod\views\layouts\modal.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1752320929.088177;i:4;a:2:{i:0;a:5:{s:4:"file";s:53:"D:\OSPanel\domains\silverzavod\views\layouts\main.php";s:4:"line";i:56;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10412352;}}}";s:9:"profiling";s:104053:"a:3:{s:6:"memory";i:10722216;s:4:"time";d:0.40445899963378906;s:8:"messages";a:102:{i:7;a:6:{i:0;s:74:"Opening DB connection: pgsql:host=127.0.0.1;port=5432;dbname=silver_test_1";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:1752320928.789553;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6059992;}i:8;a:6:{i:0;s:74:"Opening DB connection: pgsql:host=127.0.0.1;port=5432;dbname=silver_test_1";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:1752320928.851221;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6061928;}i:10;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320928.853015;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6197864;}i:11;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320928.889911;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6213224;}i:13;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320928.891756;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6308096;}i:14;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320928.896379;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6309656;}i:16;a:6:{i:0;s:63:"SELECT * FROM "users" WHERE ("id"=1) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320928.902292;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6602720;}i:17;a:6:{i:0;s:63:"SELECT * FROM "users" WHERE ("id"=1) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320928.907111;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6605728;}i:49;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='1'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320928.933538;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8807656;}i:50;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='1'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320928.937221;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8809816;}i:52;a:6:{i:0;s:46:"SELECT * FROM "auth_item" WHERE "name"='admin'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320928.937958;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8814272;}i:53;a:6:{i:0;s:46:"SELECT * FROM "auth_item" WHERE "name"='admin'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320928.941243;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8816368;}i:57;a:6:{i:0;s:428:"SELECT "expenses".*, "expenses_type"."name" AS "expense_type_name", "users"."full_name" AS "added_by_user" FROM "expenses" LEFT JOIN "expenses_type" ON expenses.expense_type_id = expenses_type.id LEFT JOIN "users" ON expenses.add_user_id = users.id WHERE ("expenses"."deleted_at" IS NULL) AND ("expenses"."created_at" >= '2025-07-12 00:00:00') AND ("expenses"."created_at" <= '2025-07-12 23:59:59') ORDER BY "expenses"."id" DESC";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320928.945176;i:4;a:1:{i:0;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:32;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8899104;}i:58;a:6:{i:0;s:428:"SELECT "expenses".*, "expenses_type"."name" AS "expense_type_name", "users"."full_name" AS "added_by_user" FROM "expenses" LEFT JOIN "expenses_type" ON expenses.expense_type_id = expenses_type.id LEFT JOIN "users" ON expenses.add_user_id = users.id WHERE ("expenses"."deleted_at" IS NULL) AND ("expenses"."created_at" >= '2025-07-12 00:00:00') AND ("expenses"."created_at" <= '2025-07-12 23:59:59') ORDER BY "expenses"."id" DESC";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320928.950724;i:4;a:1:{i:0;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:32;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8917968;}i:60;a:6:{i:0;s:2814:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'expenses'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320928.951161;i:4;a:1:{i:0;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:32;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8923232;}i:61;a:6:{i:0;s:2814:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'expenses'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320928.958477;i:4;a:1:{i:0;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:32;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8943360;}i:63;a:6:{i:0;s:876:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='expenses'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320928.959815;i:4;a:1:{i:0;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:32;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8933912;}i:64;a:6:{i:0;s:876:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='expenses'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320928.964702;i:4;a:1:{i:0;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:32;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8938072;}i:66;a:6:{i:0;s:56:"SELECT * FROM "expenses_type" WHERE "deleted_at" IS NULL";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320928.965713;i:4;a:1:{i:0;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:36;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8962880;}i:67;a:6:{i:0;s:56:"SELECT * FROM "expenses_type" WHERE "deleted_at" IS NULL";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320928.966232;i:4;a:1:{i:0;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:36;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8974040;}i:69;a:6:{i:0;s:2819:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'expenses_type'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320928.966607;i:4;a:1:{i:0;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:36;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8983776;}i:70;a:6:{i:0;s:2819:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'expenses_type'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320928.972262;i:4;a:1:{i:0;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:36;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8993000;}i:72;a:6:{i:0;s:881:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='expenses_type'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320928.972769;i:4;a:1:{i:0;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:36;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8990952;}i:73;a:6:{i:0;s:881:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='expenses_type'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320928.979324;i:4;a:1:{i:0;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:36;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8992512;}i:76;a:6:{i:0;s:126:"
        SELECT balance 
        FROM cashbox 
        WHERE title = 'Оля' AND deleted_at IS NULL
        LIMIT 1
    ";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320928.988267;i:4;a:2:{i:0;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:21;s:8:"function";s:8:"queryOne";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9393960;}i:77;a:6:{i:0;s:126:"
        SELECT balance 
        FROM cashbox 
        WHERE title = 'Оля' AND deleted_at IS NULL
        LIMIT 1
    ";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320928.991091;i:4;a:2:{i:0;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:21;s:8:"function";s:8:"queryOne";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9395568;}i:79;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=153";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9880776;}i:80;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=153";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.001056;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9884304;}i:82;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=153) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.003147;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9947136;}i:83;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=153) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.006848;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9950280;}i:85;a:6:{i:0;s:2814:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'tracking'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.007356;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9954744;}i:86;a:6:{i:0;s:2814:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'tracking'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.014465;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9969296;}i:88;a:6:{i:0;s:876:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='tracking'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.01509;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9965544;}i:89;a:6:{i:0;s:876:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='tracking'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.01882;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9967856;}i:91;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=152";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.019572;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9968552;}i:92;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=152";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.020373;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9972080;}i:94;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=152) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.020917;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9981008;}i:95;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=152) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.022125;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9984152;}i:97;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=151";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.0226;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9982344;}i:98;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=151";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.023333;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9985872;}i:100;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=151) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.023902;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9994800;}i:101;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=151) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.025142;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9997944;}i:103;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=150";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.025586;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9996136;}i:104;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=150";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.02615;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9999664;}i:106;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=150) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.026647;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10008592;}i:107;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=150) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.028134;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10011736;}i:109;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=149";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.028682;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10026312;}i:110;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=149";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.029335;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10029840;}i:112;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=149) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.029819;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10038768;}i:113;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=149) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.030961;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10041912;}i:115;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=148";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.031609;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10040104;}i:116;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=148";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.032238;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10043632;}i:118;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=148) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.032769;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10052560;}i:119;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=148) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.034024;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10055704;}i:121;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=147";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.034474;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10053896;}i:122;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=147";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.035111;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10057424;}i:124;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=147) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.035616;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10066352;}i:125;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=147) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.036912;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10069496;}i:127;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=146";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.037477;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10067688;}i:128;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=146";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.038153;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10071216;}i:130;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=146) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.038631;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10084240;}i:131;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=146) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.039996;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10087384;}i:133;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=103";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.040722;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10085576;}i:134;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=103";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.041631;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10089104;}i:136;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=103) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.042357;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10098032;}i:137;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=103) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.044558;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10101176;}i:139;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=102";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.045321;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10099368;}i:140;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=102";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.046031;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10102896;}i:142;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=102) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.046663;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10111824;}i:143;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=102) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.048325;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10114968;}i:145;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=89";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.048965;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10117256;}i:146;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=89";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.049699;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10120784;}i:148;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=89) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.050234;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10129712;}i:149;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=89) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.051763;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10132856;}i:151;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=88";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.052354;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10131048;}i:152;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=88";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.05303;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10134576;}i:154;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=88) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.053515;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10143504;}i:155;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=88) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.054716;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10146648;}i:157;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=85";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.055714;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10161224;}i:158;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=85";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.056692;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10164752;}i:160;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=85) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.057271;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10173680;}i:161;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=85) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.058521;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10176824;}i:163;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=84";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.059049;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10175016;}i:164;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=84";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.059893;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10178544;}i:166;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=84) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.060431;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10187472;}i:167;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=84) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.061728;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10190616;}i:169;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=83";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.062188;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10188808;}i:170;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=83";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.062823;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10192336;}i:172;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=83) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.063541;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10201264;}i:173;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=83) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.064847;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10204408;}i:175;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=82";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.065387;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10202600;}i:176;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=82";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.065929;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10206128;}i:178;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=82) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.066432;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10215056;}i:179;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=82) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.067622;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10218200;}i:181;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=81";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.068138;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10216392;}i:182;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=81";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.068754;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10219920;}i:184;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=81) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.069165;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10228848;}i:185;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=81) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.070255;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10231992;}i:187;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=80";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.07069;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10230184;}i:188;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=80";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.07131;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10233712;}i:190;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=80) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.071945;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10242640;}i:191;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=80) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.073736;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10245784;}}}";s:2:"db";s:103218:"a:1:{s:8:"messages";a:100:{i:10;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320928.853015;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6197864;}i:11;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320928.889911;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6213224;}i:13;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320928.891756;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6308096;}i:14;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320928.896379;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6309656;}i:16;a:6:{i:0;s:63:"SELECT * FROM "users" WHERE ("id"=1) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320928.902292;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6602720;}i:17;a:6:{i:0;s:63:"SELECT * FROM "users" WHERE ("id"=1) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320928.907111;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6605728;}i:49;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='1'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320928.933538;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8807656;}i:50;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='1'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320928.937221;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8809816;}i:52;a:6:{i:0;s:46:"SELECT * FROM "auth_item" WHERE "name"='admin'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320928.937958;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8814272;}i:53;a:6:{i:0;s:46:"SELECT * FROM "auth_item" WHERE "name"='admin'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320928.941243;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8816368;}i:57;a:6:{i:0;s:428:"SELECT "expenses".*, "expenses_type"."name" AS "expense_type_name", "users"."full_name" AS "added_by_user" FROM "expenses" LEFT JOIN "expenses_type" ON expenses.expense_type_id = expenses_type.id LEFT JOIN "users" ON expenses.add_user_id = users.id WHERE ("expenses"."deleted_at" IS NULL) AND ("expenses"."created_at" >= '2025-07-12 00:00:00') AND ("expenses"."created_at" <= '2025-07-12 23:59:59') ORDER BY "expenses"."id" DESC";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320928.945176;i:4;a:1:{i:0;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:32;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8899104;}i:58;a:6:{i:0;s:428:"SELECT "expenses".*, "expenses_type"."name" AS "expense_type_name", "users"."full_name" AS "added_by_user" FROM "expenses" LEFT JOIN "expenses_type" ON expenses.expense_type_id = expenses_type.id LEFT JOIN "users" ON expenses.add_user_id = users.id WHERE ("expenses"."deleted_at" IS NULL) AND ("expenses"."created_at" >= '2025-07-12 00:00:00') AND ("expenses"."created_at" <= '2025-07-12 23:59:59') ORDER BY "expenses"."id" DESC";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320928.950724;i:4;a:1:{i:0;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:32;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8917968;}i:60;a:6:{i:0;s:2814:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'expenses'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320928.951161;i:4;a:1:{i:0;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:32;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8923232;}i:61;a:6:{i:0;s:2814:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'expenses'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320928.958477;i:4;a:1:{i:0;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:32;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8943360;}i:63;a:6:{i:0;s:876:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='expenses'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320928.959815;i:4;a:1:{i:0;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:32;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8933912;}i:64;a:6:{i:0;s:876:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='expenses'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320928.964702;i:4;a:1:{i:0;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:32;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8938072;}i:66;a:6:{i:0;s:56:"SELECT * FROM "expenses_type" WHERE "deleted_at" IS NULL";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320928.965713;i:4;a:1:{i:0;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:36;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8962880;}i:67;a:6:{i:0;s:56:"SELECT * FROM "expenses_type" WHERE "deleted_at" IS NULL";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320928.966232;i:4;a:1:{i:0;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:36;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8974040;}i:69;a:6:{i:0;s:2819:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'expenses_type'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320928.966607;i:4;a:1:{i:0;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:36;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8983776;}i:70;a:6:{i:0;s:2819:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'expenses_type'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320928.972262;i:4;a:1:{i:0;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:36;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8993000;}i:72;a:6:{i:0;s:881:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='expenses_type'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320928.972769;i:4;a:1:{i:0;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:36;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8990952;}i:73;a:6:{i:0;s:881:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='expenses_type'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320928.979324;i:4;a:1:{i:0;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:36;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8992512;}i:76;a:6:{i:0;s:126:"
        SELECT balance 
        FROM cashbox 
        WHERE title = 'Оля' AND deleted_at IS NULL
        LIMIT 1
    ";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320928.988267;i:4;a:2:{i:0;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:21;s:8:"function";s:8:"queryOne";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9393960;}i:77;a:6:{i:0;s:126:"
        SELECT balance 
        FROM cashbox 
        WHERE title = 'Оля' AND deleted_at IS NULL
        LIMIT 1
    ";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320928.991091;i:4;a:2:{i:0;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:21;s:8:"function";s:8:"queryOne";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9395568;}i:79;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=153";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9880776;}i:80;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=153";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.001056;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9884304;}i:82;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=153) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.003147;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9947136;}i:83;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=153) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.006848;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9950280;}i:85;a:6:{i:0;s:2814:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'tracking'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.007356;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9954744;}i:86;a:6:{i:0;s:2814:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'tracking'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.014465;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9969296;}i:88;a:6:{i:0;s:876:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='tracking'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.01509;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9965544;}i:89;a:6:{i:0;s:876:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='tracking'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.01882;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9967856;}i:91;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=152";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.019572;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9968552;}i:92;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=152";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.020373;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9972080;}i:94;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=152) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.020917;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9981008;}i:95;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=152) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.022125;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9984152;}i:97;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=151";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.0226;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9982344;}i:98;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=151";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.023333;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9985872;}i:100;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=151) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.023902;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9994800;}i:101;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=151) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.025142;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9997944;}i:103;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=150";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.025586;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9996136;}i:104;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=150";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.02615;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9999664;}i:106;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=150) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.026647;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10008592;}i:107;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=150) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.028134;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10011736;}i:109;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=149";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.028682;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10026312;}i:110;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=149";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.029335;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10029840;}i:112;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=149) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.029819;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10038768;}i:113;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=149) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.030961;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10041912;}i:115;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=148";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.031609;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10040104;}i:116;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=148";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.032238;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10043632;}i:118;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=148) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.032769;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10052560;}i:119;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=148) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.034024;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10055704;}i:121;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=147";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.034474;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10053896;}i:122;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=147";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.035111;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10057424;}i:124;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=147) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.035616;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10066352;}i:125;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=147) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.036912;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10069496;}i:127;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=146";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.037477;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10067688;}i:128;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=146";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.038153;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10071216;}i:130;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=146) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.038631;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10084240;}i:131;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=146) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.039996;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10087384;}i:133;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=103";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.040722;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10085576;}i:134;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=103";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.041631;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10089104;}i:136;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=103) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.042357;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10098032;}i:137;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=103) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.044558;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10101176;}i:139;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=102";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.045321;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10099368;}i:140;a:6:{i:0;s:39:"SELECT * FROM "expenses" WHERE "id"=102";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.046031;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10102896;}i:142;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=102) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.046663;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10111824;}i:143;a:6:{i:0;s:151:"SELECT * FROM "tracking" WHERE ("process_id"=102) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.048325;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10114968;}i:145;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=89";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.048965;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10117256;}i:146;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=89";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.049699;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10120784;}i:148;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=89) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.050234;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10129712;}i:149;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=89) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.051763;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10132856;}i:151;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=88";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.052354;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10131048;}i:152;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=88";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.05303;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10134576;}i:154;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=88) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.053515;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10143504;}i:155;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=88) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.054716;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10146648;}i:157;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=85";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.055714;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10161224;}i:158;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=85";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.056692;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10164752;}i:160;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=85) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.057271;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10173680;}i:161;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=85) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.058521;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10176824;}i:163;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=84";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.059049;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10175016;}i:164;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=84";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.059893;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10178544;}i:166;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=84) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.060431;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10187472;}i:167;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=84) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.061728;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10190616;}i:169;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=83";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.062188;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10188808;}i:170;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=83";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.062823;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10192336;}i:172;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=83) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.063541;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10201264;}i:173;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=83) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.064847;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10204408;}i:175;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=82";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.065387;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10202600;}i:176;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=82";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.065929;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10206128;}i:178;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=82) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.066432;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10215056;}i:179;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=82) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.067622;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10218200;}i:181;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=81";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.068138;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10216392;}i:182;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=81";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.068754;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10219920;}i:184;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=81) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.069165;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10228848;}i:185;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=81) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.070255;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10231992;}i:187;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=80";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.07069;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10230184;}i:188;a:6:{i:0;s:38:"SELECT * FROM "expenses" WHERE "id"=80";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.07131;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:188;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10233712;}i:190;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=80) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.071945;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10242640;}i:191;a:6:{i:0;s:150:"SELECT * FROM "tracking" WHERE ("process_id"=80) AND ("progress_type" IN (1, 5, 6, 7, 2, 4, 3)) AND ("accepted_at" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752320929.073736;i:4;a:3:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\silverzavod\modules\backend\models\Expenses.php";s:4:"line";i:215;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"D:\OSPanel\domains\silverzavod\modules\backend\views\expenses\index.php";s:4:"line";i:311;s:8:"function";s:11:"canBeEdited";s:5:"class";s:35:"app\modules\backend\models\Expenses";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:81:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\ExpensesController.php";s:4:"line";i:40;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10245784;}}}";s:5:"event";s:36730:"a:202:{i:0;a:5:{s:4:"time";d:1752320928.783696;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:1;a:5:{s:4:"time";d:1752320928.851206;s:4:"name";s:9:"afterOpen";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:2;a:5:{s:4:"time";d:1752320928.907386;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:32:"app\modules\backend\models\Users";}i:3;a:5:{s:4:"time";d:1752320928.907463;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:32:"app\modules\backend\models\Users";}i:4;a:5:{s:4:"time";d:1752320928.915148;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:32:"app\modules\backend\models\Users";}i:5;a:5:{s:4:"time";d:1752320928.923689;s:4:"name";s:13:"beforeRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:6;a:5:{s:4:"time";d:1752320928.9271;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:7;a:5:{s:4:"time";d:1752320928.927113;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"app\modules\backend\BackendModule";}i:8;a:5:{s:4:"time";d:1752320928.930486;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:9;a:5:{s:4:"time";d:1752320928.930515;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:10;a:5:{s:4:"time";d:1752320928.930527;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:11;a:5:{s:4:"time";d:1752320928.930536;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:12;a:5:{s:4:"time";d:1752320928.930544;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:13;a:5:{s:4:"time";d:1752320928.930552;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:14;a:5:{s:4:"time";d:1752320928.930559;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:15;a:5:{s:4:"time";d:1752320928.930604;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:50:"app\modules\backend\controllers\ExpensesController";}i:16;a:5:{s:4:"time";d:1752320928.943015;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:17;a:5:{s:4:"time";d:1752320928.965558;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:18;a:5:{s:4:"time";d:1752320928.966492;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:19;a:5:{s:4:"time";d:1752320928.979867;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:20;a:5:{s:4:"time";d:1752320928.979894;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:21;a:5:{s:4:"time";d:1752320928.979906;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:22;a:5:{s:4:"time";d:1752320928.979918;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:23;a:5:{s:4:"time";d:1752320928.979929;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:24;a:5:{s:4:"time";d:1752320928.97994;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:25;a:5:{s:4:"time";d:1752320928.979951;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:26;a:5:{s:4:"time";d:1752320928.979961;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:27;a:5:{s:4:"time";d:1752320928.979972;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:28;a:5:{s:4:"time";d:1752320928.979984;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:29;a:5:{s:4:"time";d:1752320928.979995;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:30;a:5:{s:4:"time";d:1752320928.980005;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:31;a:5:{s:4:"time";d:1752320928.980015;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:32;a:5:{s:4:"time";d:1752320928.980026;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:33;a:5:{s:4:"time";d:1752320928.980037;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:34;a:5:{s:4:"time";d:1752320928.980049;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:35;a:5:{s:4:"time";d:1752320928.98006;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:36;a:5:{s:4:"time";d:1752320928.980073;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:37;a:5:{s:4:"time";d:1752320928.980079;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:38;a:5:{s:4:"time";d:1752320928.980084;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:39;a:5:{s:4:"time";d:1752320928.980088;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:40;a:5:{s:4:"time";d:1752320928.980093;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:41;a:5:{s:4:"time";d:1752320928.980098;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:42;a:5:{s:4:"time";d:1752320928.980102;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:43;a:5:{s:4:"time";d:1752320928.980107;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:44;a:5:{s:4:"time";d:1752320928.980112;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:45;a:5:{s:4:"time";d:1752320928.980117;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:46;a:5:{s:4:"time";d:1752320928.980122;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:47;a:5:{s:4:"time";d:1752320928.980127;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:48;a:5:{s:4:"time";d:1752320928.980132;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:49;a:5:{s:4:"time";d:1752320928.980137;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:50;a:5:{s:4:"time";d:1752320928.980141;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:51;a:5:{s:4:"time";d:1752320928.980146;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:52;a:5:{s:4:"time";d:1752320928.980151;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:53;a:5:{s:4:"time";d:1752320928.980156;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\modules\backend\models\ExpensesType";}i:54;a:5:{s:4:"time";d:1752320928.983065;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:55;a:5:{s:4:"time";d:1752320928.996866;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:56;a:5:{s:4:"time";d:1752320928.996905;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:57;a:5:{s:4:"time";d:1752320928.99692;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:58;a:5:{s:4:"time";d:1752320928.996933;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:59;a:5:{s:4:"time";d:1752320928.996945;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:60;a:5:{s:4:"time";d:1752320928.996958;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:61;a:5:{s:4:"time";d:1752320928.99697;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:62;a:5:{s:4:"time";d:1752320928.996983;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:63;a:5:{s:4:"time";d:1752320928.996995;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:64;a:5:{s:4:"time";d:1752320928.997007;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:65;a:5:{s:4:"time";d:1752320928.99702;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:66;a:5:{s:4:"time";d:1752320928.997031;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:67;a:5:{s:4:"time";d:1752320928.997043;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:68;a:5:{s:4:"time";d:1752320928.999439;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\widgets\Pjax";}i:69;a:5:{s:4:"time";d:1752320928.999734;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:70;a:5:{s:4:"time";d:1752320929.001339;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:71;a:5:{s:4:"time";d:1752320929.001399;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:72;a:5:{s:4:"time";d:1752320929.001785;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:73;a:5:{s:4:"time";d:1752320929.007163;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:74;a:5:{s:4:"time";d:1752320929.019321;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:75;a:5:{s:4:"time";d:1752320929.019435;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:76;a:5:{s:4:"time";d:1752320929.020646;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:77;a:5:{s:4:"time";d:1752320929.020701;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:78;a:5:{s:4:"time";d:1752320929.020732;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:79;a:5:{s:4:"time";d:1752320929.022311;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:80;a:5:{s:4:"time";d:1752320929.022351;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:81;a:5:{s:4:"time";d:1752320929.022433;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:82;a:5:{s:4:"time";d:1752320929.023654;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:83;a:5:{s:4:"time";d:1752320929.023704;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:84;a:5:{s:4:"time";d:1752320929.023733;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:85;a:5:{s:4:"time";d:1752320929.025334;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:86;a:5:{s:4:"time";d:1752320929.025377;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:87;a:5:{s:4:"time";d:1752320929.025466;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:88;a:5:{s:4:"time";d:1752320929.026329;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:89;a:5:{s:4:"time";d:1752320929.026379;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:90;a:5:{s:4:"time";d:1752320929.026406;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:91;a:5:{s:4:"time";d:1752320929.028426;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:92;a:5:{s:4:"time";d:1752320929.028472;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:93;a:5:{s:4:"time";d:1752320929.028561;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:94;a:5:{s:4:"time";d:1752320929.029549;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:95;a:5:{s:4:"time";d:1752320929.029603;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:96;a:5:{s:4:"time";d:1752320929.029633;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:97;a:5:{s:4:"time";d:1752320929.031346;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:98;a:5:{s:4:"time";d:1752320929.031398;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:99;a:5:{s:4:"time";d:1752320929.031485;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:100;a:5:{s:4:"time";d:1752320929.032521;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:101;a:5:{s:4:"time";d:1752320929.03257;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:102;a:5:{s:4:"time";d:1752320929.032598;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:103;a:5:{s:4:"time";d:1752320929.034271;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:104;a:5:{s:4:"time";d:1752320929.034305;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:105;a:5:{s:4:"time";d:1752320929.034379;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:106;a:5:{s:4:"time";d:1752320929.03539;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:107;a:5:{s:4:"time";d:1752320929.035432;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:108;a:5:{s:4:"time";d:1752320929.035459;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:109;a:5:{s:4:"time";d:1752320929.037224;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:110;a:5:{s:4:"time";d:1752320929.03727;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:111;a:5:{s:4:"time";d:1752320929.037356;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:112;a:5:{s:4:"time";d:1752320929.038358;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:113;a:5:{s:4:"time";d:1752320929.038409;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:114;a:5:{s:4:"time";d:1752320929.038437;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:115;a:5:{s:4:"time";d:1752320929.040392;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:116;a:5:{s:4:"time";d:1752320929.040448;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:117;a:5:{s:4:"time";d:1752320929.040565;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:118;a:5:{s:4:"time";d:1752320929.041968;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:119;a:5:{s:4:"time";d:1752320929.042054;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:120;a:5:{s:4:"time";d:1752320929.042103;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:121;a:5:{s:4:"time";d:1752320929.044955;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:122;a:5:{s:4:"time";d:1752320929.045034;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:123;a:5:{s:4:"time";d:1752320929.045159;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:124;a:5:{s:4:"time";d:1752320929.046251;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:125;a:5:{s:4:"time";d:1752320929.046314;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:126;a:5:{s:4:"time";d:1752320929.046349;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:127;a:5:{s:4:"time";d:1752320929.048669;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:128;a:5:{s:4:"time";d:1752320929.048723;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:129;a:5:{s:4:"time";d:1752320929.048825;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:130;a:5:{s:4:"time";d:1752320929.049932;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:131;a:5:{s:4:"time";d:1752320929.049994;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:132;a:5:{s:4:"time";d:1752320929.050031;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:133;a:5:{s:4:"time";d:1752320929.052085;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:134;a:5:{s:4:"time";d:1752320929.052134;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:135;a:5:{s:4:"time";d:1752320929.052226;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:136;a:5:{s:4:"time";d:1752320929.053245;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:137;a:5:{s:4:"time";d:1752320929.053299;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:138;a:5:{s:4:"time";d:1752320929.05333;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:139;a:5:{s:4:"time";d:1752320929.055111;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:140;a:5:{s:4:"time";d:1752320929.055183;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:141;a:5:{s:4:"time";d:1752320929.055479;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:142;a:5:{s:4:"time";d:1752320929.057;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:143;a:5:{s:4:"time";d:1752320929.057055;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:144;a:5:{s:4:"time";d:1752320929.057085;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:145;a:5:{s:4:"time";d:1752320929.058818;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:146;a:5:{s:4:"time";d:1752320929.058856;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:147;a:5:{s:4:"time";d:1752320929.05894;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:148;a:5:{s:4:"time";d:1752320929.060164;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:149;a:5:{s:4:"time";d:1752320929.060218;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:150;a:5:{s:4:"time";d:1752320929.060249;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:151;a:5:{s:4:"time";d:1752320929.061928;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:152;a:5:{s:4:"time";d:1752320929.061972;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:153;a:5:{s:4:"time";d:1752320929.062069;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:154;a:5:{s:4:"time";d:1752320929.063274;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:155;a:5:{s:4:"time";d:1752320929.063329;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:156;a:5:{s:4:"time";d:1752320929.06336;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:157;a:5:{s:4:"time";d:1752320929.065139;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:158;a:5:{s:4:"time";d:1752320929.065184;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:159;a:5:{s:4:"time";d:1752320929.065268;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:160;a:5:{s:4:"time";d:1752320929.066179;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:161;a:5:{s:4:"time";d:1752320929.06623;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:162;a:5:{s:4:"time";d:1752320929.066259;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:163;a:5:{s:4:"time";d:1752320929.067838;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:164;a:5:{s:4:"time";d:1752320929.067936;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:165;a:5:{s:4:"time";d:1752320929.068022;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:166;a:5:{s:4:"time";d:1752320929.068925;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:167;a:5:{s:4:"time";d:1752320929.068972;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:168;a:5:{s:4:"time";d:1752320929.068999;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:169;a:5:{s:4:"time";d:1752320929.070445;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:170;a:5:{s:4:"time";d:1752320929.070485;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:171;a:5:{s:4:"time";d:1752320929.070569;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:172;a:5:{s:4:"time";d:1752320929.071649;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:173;a:5:{s:4:"time";d:1752320929.071709;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"app\modules\backend\models\Expenses";}i:174;a:5:{s:4:"time";d:1752320929.071743;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:175;a:5:{s:4:"time";d:1752320929.07408;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:176;a:5:{s:4:"time";d:1752320929.07414;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:177;a:5:{s:4:"time";d:1752320929.07505;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\widgets\Pjax";}i:178;a:5:{s:4:"time";d:1752320929.07984;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\widgets\Pjax";}i:179;a:5:{s:4:"time";d:1752320929.079967;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:180;a:5:{s:4:"time";d:1752320929.080217;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:181;a:5:{s:4:"time";d:1752320929.080951;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:182;a:5:{s:4:"time";d:1752320929.081518;s:4:"name";s:9:"beginPage";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:183;a:5:{s:4:"time";d:1752320929.082612;s:4:"name";s:9:"beginBody";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:184;a:5:{s:4:"time";d:1752320929.083086;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:185;a:5:{s:4:"time";d:1752320929.084279;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:186;a:5:{s:4:"time";d:1752320929.08628;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:187;a:5:{s:4:"time";d:1752320929.086478;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:188;a:5:{s:4:"time";d:1752320929.086819;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:189;a:5:{s:4:"time";d:1752320929.087403;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:190;a:5:{s:4:"time";d:1752320929.088162;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:191;a:5:{s:4:"time";d:1752320929.088942;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:192;a:5:{s:4:"time";d:1752320929.090808;s:4:"name";s:7:"endBody";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:193;a:5:{s:4:"time";d:1752320929.091947;s:4:"name";s:7:"endPage";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:194;a:5:{s:4:"time";d:1752320929.09296;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:195;a:5:{s:4:"time";d:1752320929.093056;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:50:"app\modules\backend\controllers\ExpensesController";}i:196;a:5:{s:4:"time";d:1752320929.093079;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"app\modules\backend\BackendModule";}i:197;a:5:{s:4:"time";d:1752320929.093098;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:198;a:5:{s:4:"time";d:1752320929.093123;s:4:"name";s:12:"afterRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:199;a:5:{s:4:"time";d:1752320929.093141;s:4:"name";s:10:"beforeSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:200;a:5:{s:4:"time";d:1752320929.094598;s:4:"name";s:12:"afterPrepare";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:201;a:5:{s:4:"time";d:1752320929.095319;s:4:"name";s:9:"afterSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}}";s:4:"mail";s:6:"a:0:{}";s:8:"timeline";s:92:"a:3:{s:5:"start";d:1752320928.693012;s:3:"end";d:1752320929.100507;s:6:"memory";i:10722216;}";s:4:"dump";s:6:"a:0:{}";s:6:"router";s:4861:"a:3:{s:8:"messages";a:24:{i:21;a:6:{i:0;a:3:{s:4:"rule";s:3:"gii";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752320928.924149;i:4;a:0:{}i:5;i:8136240;}i:22;a:6:{i:0;a:3:{s:4:"rule";s:12:"gii/<id:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752320928.924163;i:4;a:0:{}i:5;i:8136992;}i:23;a:6:{i:0;a:3:{s:4:"rule";s:41:"gii/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752320928.92417;i:4;a:0:{}i:5;i:8137744;}i:24;a:6:{i:0;a:3:{s:4:"rule";s:5:"debug";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752320928.924175;i:4;a:0:{}i:5;i:8138496;}i:25;a:6:{i:0;a:3:{s:4:"rule";s:43:"debug/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752320928.92418;i:4;a:0:{}i:5;i:8139248;}i:26;a:6:{i:0;a:3:{s:4:"rule";s:1:"/";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752320928.924185;i:4;a:0:{}i:5;i:8140000;}i:27;a:6:{i:0;a:3:{s:4:"rule";s:5:"login";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752320928.924189;i:4;a:0:{}i:5;i:8140752;}i:28;a:6:{i:0;a:3:{s:4:"rule";s:6:"logout";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752320928.924193;i:4;a:0:{}i:5;i:8141504;}i:29;a:6:{i:0;a:3:{s:4:"rule";s:13:"site/<action>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752320928.924197;i:4;a:0:{}i:5;i:8142256;}i:30;a:6:{i:0;a:3:{s:4:"rule";s:39:"api/<controller:[\w-]+>/<action:[\w-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752320928.924202;i:4;a:0:{}i:5;i:8143008;}i:31;a:6:{i:0;a:3:{s:4:"rule";s:23:"api/<controller:[\w-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752320928.924206;i:4;a:0:{}i:5;i:8143760;}i:32;a:6:{i:0;a:3:{s:4:"rule";s:12:"swagger/json";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752320928.92421;i:4;a:0:{}i:5;i:8144512;}i:33;a:6:{i:0;a:3:{s:4:"rule";s:10:"swagger/ui";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752320928.924215;i:4;a:0:{}i:5;i:8146544;}i:34;a:6:{i:0;a:3:{s:4:"rule";s:7:"swagger";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752320928.924219;i:4;a:0:{}i:5;i:8147296;}i:35;a:6:{i:0;a:3:{s:4:"rule";s:25:"backend/position/<action>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752320928.924224;i:4;a:0:{}i:5;i:8148048;}i:36;a:6:{i:0;a:3:{s:4:"rule";s:16:"backend/position";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752320928.924228;i:4;a:0:{}i:5;i:8148800;}i:37;a:6:{i:0;a:3:{s:4:"rule";s:32:"backend/equipment/<id:\d+>/parts";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752320928.924232;i:4;a:0:{}i:5;i:8149552;}i:38;a:6:{i:0;a:3:{s:4:"rule";s:60:"backend/equipment/defect-part/<equipmentId:\d+>/<partId:\d+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752320928.924237;i:4;a:0:{}i:5;i:8150304;}i:39;a:6:{i:0;a:3:{s:4:"rule";s:61:"backend/equipment/reserve-part/<equipmentId:\d+>/<partId:\d+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752320928.924241;i:4;a:0:{}i:5;i:8151056;}i:40;a:6:{i:0;a:3:{s:4:"rule";s:60:"backend/equipment/repair-part/<equipmentId:\d+>/<partId:\d+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752320928.924245;i:4;a:0:{}i:5;i:8151808;}i:41;a:6:{i:0;a:3:{s:4:"rule";s:62:"backend/equipment/activate-part/<equipmentId:\d+>/<partId:\d+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752320928.92425;i:4;a:0:{}i:5;i:8152560;}i:42;a:6:{i:0;a:3:{s:4:"rule";s:38:"backend/<controller>/<action>/<id:\d+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752320928.924255;i:4;a:0:{}i:5;i:8153312;}i:43;a:6:{i:0;s:59:"Request parsed with URL rule: backend/<controller>/<action>";i:1;i:8;i:2;s:29:"yii\web\UrlRule::parseRequest";i:3;d:1752320928.924271;i:4;a:0:{}i:5;i:8155376;}i:44;a:6:{i:0;a:3:{s:4:"rule";s:29:"backend/<controller>/<action>";s:5:"match";b:1;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752320928.924276;i:4;a:0:{}i:5;i:8155224;}}s:5:"route";s:22:"backend/expenses/index";s:6:"action";s:65:"app\modules\backend\controllers\ExpensesController::actionIndex()";}";s:7:"request";s:5652:"a:15:{s:7:"flashes";a:0:{}s:10:"statusCode";i:200;s:14:"requestHeaders";a:9:{s:4:"host";s:6:"silver";s:10:"connection";s:10:"keep-alive";s:25:"upgrade-insecure-requests";s:1:"1";s:10:"user-agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:6:"accept";s:135:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7";s:7:"referer";s:42:"http://silver/backend/worker-payment/index";s:15:"accept-encoding";s:13:"gzip, deflate";s:15:"accept-language";s:65:"en-RU,en;q=0.9,ru-RU;q=0.8,ru;q=0.7,en-US;q=0.6,uz;q=0.5,hr;q=0.4";s:6:"cookie";s:539:"language=4e1a22a706b2b67a2ac68d2d85cb839925d09aed085ca214408c5498b87a9ea2a%3A2%3A%7Bi%3A0%3Bs%3A8%3A%22language%22%3Bi%3A1%3Bs%3A2%3A%22uz%22%3B%7D; PHPSESSID=tg16mblupj6u875l5vqjrkigi7pqr8f3; _identity=d3bbf1a40fbb07a5e71f4b657b0d9caaa7c20725eb7f86d2213424408f95f8caa%3A2%3A%7Bi%3A0%3Bs%3A9%3A%22_identity%22%3Bi%3A1%3Bs%3A16%3A%22%5B1%2Cnull%2C2592000%5D%22%3B%7D; _csrf=5de5c8623f8b1f477dee3e2ed0014819171dc78456feef3a897ed3d5ed083c16a%3A2%3A%7Bi%3A0%3Bs%3A5%3A%22_csrf%22%3Bi%3A1%3Bs%3A32%3A%22q6T-dtfJoJoonDmVVkMNyEhTWI1Kz4Cz%22%3B%7D";}s:15:"responseHeaders";a:8:{s:7:"Expires";s:29:"Thu, 19 Nov 1981 08:52:00 GMT";s:13:"Cache-Control";s:35:"no-store, no-cache, must-revalidate";s:6:"Pragma";s:8:"no-cache";s:12:"Content-Type";s:24:"text/html; charset=UTF-8";s:11:"X-Debug-Tag";s:13:"68724ba0e0691";s:16:"X-Debug-Duration";s:3:"403";s:12:"X-Debug-Link";s:37:"/debug/default/view?tag=68724ba0e0691";s:10:"Set-Cookie";s:260:"_identity=d3bbf1a40fbb07a5e71f4b657b0d9caaa7c20725eb7f86d2213424408f95f8caa%3A2%3A%7Bi%3A0%3Bs%3A9%3A%22_identity%22%3Bi%3A1%3Bs%3A16%3A%22%5B1%2Cnull%2C2592000%5D%22%3B%7D; expires=Mon, 11-Aug-2025 11:48:48 GMT; Max-Age=2591999; path=/; HttpOnly; SameSite=Lax";}s:5:"route";s:22:"backend/expenses/index";s:6:"action";s:65:"app\modules\backend\controllers\ExpensesController::actionIndex()";s:12:"actionParams";a:0:{}s:7:"general";a:5:{s:6:"method";s:3:"GET";s:6:"isAjax";b:0;s:6:"isPjax";b:0;s:7:"isFlash";b:0;s:18:"isSecureConnection";b:0;}s:11:"requestBody";a:0:{}s:6:"SERVER";a:38:{s:15:"REDIRECT_STATUS";s:3:"200";s:9:"HTTP_HOST";s:6:"silver";s:15:"HTTP_CONNECTION";s:10:"keep-alive";s:30:"HTTP_UPGRADE_INSECURE_REQUESTS";s:1:"1";s:15:"HTTP_USER_AGENT";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:11:"HTTP_ACCEPT";s:135:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7";s:12:"HTTP_REFERER";s:42:"http://silver/backend/worker-payment/index";s:20:"HTTP_ACCEPT_ENCODING";s:13:"gzip, deflate";s:20:"HTTP_ACCEPT_LANGUAGE";s:65:"en-RU,en;q=0.9,ru-RU;q=0.8,ru;q=0.7,en-US;q=0.6,uz;q=0.5,hr;q=0.4";s:11:"HTTP_COOKIE";s:539:"language=4e1a22a706b2b67a2ac68d2d85cb839925d09aed085ca214408c5498b87a9ea2a%3A2%3A%7Bi%3A0%3Bs%3A8%3A%22language%22%3Bi%3A1%3Bs%3A2%3A%22uz%22%3B%7D; PHPSESSID=tg16mblupj6u875l5vqjrkigi7pqr8f3; _identity=d3bbf1a40fbb07a5e71f4b657b0d9caaa7c20725eb7f86d2213424408f95f8caa%3A2%3A%7Bi%3A0%3Bs%3A9%3A%22_identity%22%3Bi%3A1%3Bs%3A16%3A%22%5B1%2Cnull%2C2592000%5D%22%3B%7D; _csrf=5de5c8623f8b1f477dee3e2ed0014819171dc78456feef3a897ed3d5ed083c16a%3A2%3A%7Bi%3A0%3Bs%3A5%3A%22_csrf%22%3Bi%3A1%3Bs%3A32%3A%22q6T-dtfJoJoonDmVVkMNyEhTWI1Kz4Cz%22%3B%7D";s:4:"PATH";s:343:"d:\ospanel\modules\php\PHP_8.1\ext;d:\ospanel\modules\php\PHP_8.1\pear;d:\ospanel\modules\php\PHP_8.1\pear\bin;d:\ospanel\modules\php\PHP_8.1;d:\ospanel\modules\wget\bin;d:\ospanel\modules\http\Apache_2.4-PHP_8.0-8.1\bin;d:\ospanel\modules\http\Apache_2.4-PHP_8.0-8.1;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\system32\Wbem;C:\WINDOWS\SysWOW64";s:10:"SystemRoot";s:10:"C:\WINDOWS";s:7:"COMSPEC";s:27:"C:\WINDOWS\system32\cmd.exe";s:7:"PATHEXT";s:62:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW";s:6:"WINDIR";s:10:"C:\WINDOWS";s:16:"SERVER_SIGNATURE";s:0:"";s:15:"SERVER_SOFTWARE";s:6:"Apache";s:11:"SERVER_NAME";s:6:"silver";s:11:"SERVER_ADDR";s:9:"127.0.0.1";s:11:"SERVER_PORT";s:2:"80";s:11:"REMOTE_ADDR";s:9:"127.0.0.1";s:13:"DOCUMENT_ROOT";s:34:"D:/OSPanel/domains/silverzavod/web";s:14:"REQUEST_SCHEME";s:4:"http";s:14:"CONTEXT_PREFIX";s:0:"";s:21:"CONTEXT_DOCUMENT_ROOT";s:34:"D:/OSPanel/domains/silverzavod/web";s:12:"SERVER_ADMIN";s:18:"[no address given]";s:15:"SCRIPT_FILENAME";s:44:"D:/OSPanel/domains/silverzavod/web/index.php";s:11:"REMOTE_PORT";s:5:"55877";s:12:"REDIRECT_URL";s:23:"/backend/expenses/index";s:17:"GATEWAY_INTERFACE";s:7:"CGI/1.1";s:15:"SERVER_PROTOCOL";s:8:"HTTP/1.1";s:14:"REQUEST_METHOD";s:3:"GET";s:12:"QUERY_STRING";s:0:"";s:11:"REQUEST_URI";s:23:"/backend/expenses/index";s:11:"SCRIPT_NAME";s:10:"/index.php";s:8:"PHP_SELF";s:10:"/index.php";s:18:"REQUEST_TIME_FLOAT";d:1752320928.673096;s:12:"REQUEST_TIME";i:1752320928;}s:3:"GET";a:0:{}s:4:"POST";a:0:{}s:6:"COOKIE";a:4:{s:8:"language";s:102:"4e1a22a706b2b67a2ac68d2d85cb839925d09aed085ca214408c5498b87a9ea2a:2:{i:0;s:8:"language";i:1;s:2:"uz";}";s:9:"PHPSESSID";s:32:"tg16mblupj6u875l5vqjrkigi7pqr8f3";s:9:"_identity";s:118:"d3bbf1a40fbb07a5e71f4b657b0d9caaa7c20725eb7f86d2213424408f95f8caa:2:{i:0;s:9:"_identity";i:1;s:16:"[1,null,2592000]";}";s:5:"_csrf";s:130:"5de5c8623f8b1f477dee3e2ed0014819171dc78456feef3a897ed3d5ed083c16a:2:{i:0;s:5:"_csrf";i:1;s:32:"q6T-dtfJoJoonDmVVkMNyEhTWI1Kz4Cz";}";}s:5:"FILES";a:0:{}s:7:"SESSION";a:6:{s:7:"__flash";a:0:{}s:4:"__id";i:1;s:9:"__authKey";N;s:8:"__expire";i:1752328128;s:13:"last_activity";i:1752320928;s:7:"timeout";i:1800;}}";s:4:"user";s:2148:"a:5:{s:2:"id";i:1;s:8:"identity";a:8:{s:2:"id";s:1:"1";s:8:"username";s:7:"'admin'";s:9:"full_name";s:15:"'Administrator'";s:4:"role";s:1:"1";s:12:"access_token";s:42:"'Am4PA_XsYEchREdnLU19DkEKnev9Tqn03HNbrXNd'";s:8:"password";s:62:"'$2y$13$0eRlRvvyoXFPe1EZQr1a1OJtiuhoGEzSLGqLV0XWtF3ZVbwKTSkjO'";s:10:"created_at";s:21:"'2025-02-25 12:53:18'";s:10:"deleted_at";s:4:"null";}s:10:"attributes";a:8:{i:0;a:2:{s:9:"attribute";s:2:"id";s:5:"label";s:2:"ID";}i:1;a:2:{s:9:"attribute";s:8:"username";s:5:"label";s:10:"Логин";}i:2;a:2:{s:9:"attribute";s:9:"full_name";s:5:"label";s:6:"ФИО";}i:3;a:2:{s:9:"attribute";s:4:"role";s:5:"label";s:4:"Role";}i:4;a:2:{s:9:"attribute";s:12:"access_token";s:5:"label";s:12:"Access Token";}i:5;a:2:{s:9:"attribute";s:8:"password";s:5:"label";s:12:"Пароль";}i:6;a:2:{s:9:"attribute";s:10:"created_at";s:5:"label";s:25:"Дата создания";}i:7;a:2:{s:9:"attribute";s:10:"deleted_at";s:5:"label";s:25:"Дата удаления";}}s:13:"rolesProvider";O:26:"yii\data\ArrayDataProvider":12:{s:27:" yii\base\Component _events";a:0:{}s:35:" yii\base\Component _eventWildcards";a:0:{}s:30:" yii\base\Component _behaviors";N;s:2:"id";N;s:32:" yii\data\BaseDataProvider _sort";N;s:38:" yii\data\BaseDataProvider _pagination";N;s:32:" yii\data\BaseDataProvider _keys";N;s:34:" yii\data\BaseDataProvider _models";N;s:38:" yii\data\BaseDataProvider _totalCount";N;s:3:"key";N;s:9:"allModels";a:1:{s:5:"admin";a:7:{s:4:"type";i:1;s:4:"name";s:5:"admin";s:11:"description";s:13:"Administrator";s:8:"ruleName";N;s:4:"data";s:4:"null";s:9:"createdAt";i:**********;s:9:"updatedAt";i:**********;}}s:10:"modelClass";N;}s:19:"permissionsProvider";O:26:"yii\data\ArrayDataProvider":12:{s:27:" yii\base\Component _events";a:0:{}s:35:" yii\base\Component _eventWildcards";a:0:{}s:30:" yii\base\Component _behaviors";N;s:2:"id";s:4:"dp-1";s:32:" yii\data\BaseDataProvider _sort";N;s:38:" yii\data\BaseDataProvider _pagination";N;s:32:" yii\data\BaseDataProvider _keys";N;s:34:" yii\data\BaseDataProvider _models";N;s:38:" yii\data\BaseDataProvider _totalCount";N;s:3:"key";N;s:9:"allModels";a:0:{}s:10:"modelClass";N;}}";s:5:"asset";s:4902:"a:8:{s:26:"app\assets\DataTablesAsset";a:9:{s:10:"sourcePath";N;s:8:"basePath";s:34:"D:/OSPanel/domains/silverzavod/web";s:7:"baseUrl";s:0:"";s:7:"depends";a:3:{i:0;s:19:"yii\web\JqueryAsset";i:1;s:29:"yii\bootstrap4\BootstrapAsset";i:2;s:35:"yii\bootstrap4\BootstrapPluginAsset";}s:2:"js";a:3:{i:0;s:73:"https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.7/umd/popper.min.js";i:1;s:61:"https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js";i:2;s:65:"https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap4.min.js";}s:3:"css";a:1:{i:0;s:67:"https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap4.min.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:19:"yii\web\JqueryAsset";a:9:{s:10:"sourcePath";s:61:"D:\OSPanel\domains\silverzavod\vendor/bower-asset/jquery/dist";s:8:"basePath";s:50:"D:\OSPanel\domains\silverzavod\web\assets\e0ff1908";s:7:"baseUrl";s:16:"/assets/e0ff1908";s:7:"depends";a:0:{}s:2:"js";a:1:{i:0;s:9:"jquery.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:29:"yii\bootstrap4\BootstrapAsset";a:9:{s:10:"sourcePath";s:62:"D:\OSPanel\domains\silverzavod\vendor/npm-asset/bootstrap/dist";s:8:"basePath";s:50:"D:\OSPanel\domains\silverzavod\web\assets\c3f9d53c";s:7:"baseUrl";s:16:"/assets/c3f9d53c";s:7:"depends";a:0:{}s:2:"js";a:0:{}s:3:"css";a:1:{i:0;s:17:"css/bootstrap.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:35:"yii\bootstrap4\BootstrapPluginAsset";a:9:{s:10:"sourcePath";s:62:"D:\OSPanel\domains\silverzavod\vendor/npm-asset/bootstrap/dist";s:8:"basePath";s:50:"D:\OSPanel\domains\silverzavod\web\assets\c3f9d53c";s:7:"baseUrl";s:16:"/assets/c3f9d53c";s:7:"depends";a:2:{i:0;s:19:"yii\web\JqueryAsset";i:1;s:29:"yii\bootstrap4\BootstrapAsset";}s:2:"js";a:1:{i:0;s:22:"js/bootstrap.bundle.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:23:"app\assets\Select2Asset";a:9:{s:10:"sourcePath";N;s:8:"basePath";s:34:"D:/OSPanel/domains/silverzavod/web";s:7:"baseUrl";s:0:"";s:7:"depends";a:1:{i:0;s:19:"yii\web\JqueryAsset";}s:2:"js";a:1:{i:0;s:70:"https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js";}s:3:"css";a:1:{i:0;s:72:"https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:21:"yii\widgets\PjaxAsset";a:9:{s:10:"sourcePath";s:59:"D:\OSPanel\domains\silverzavod\vendor/bower-asset/yii2-pjax";s:8:"basePath";s:50:"D:\OSPanel\domains\silverzavod\web\assets\99d1aff7";s:7:"baseUrl";s:16:"/assets/99d1aff7";s:7:"depends";a:1:{i:0;s:16:"yii\web\YiiAsset";}s:2:"js";a:1:{i:0;s:14:"jquery.pjax.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:16:"yii\web\YiiAsset";a:9:{s:10:"sourcePath";s:57:"D:\OSPanel\domains\silverzavod\vendor\yiisoft\yii2/assets";s:8:"basePath";s:50:"D:\OSPanel\domains\silverzavod\web\assets\e0686f09";s:7:"baseUrl";s:16:"/assets/e0686f09";s:7:"depends";a:1:{i:0;s:19:"yii\web\JqueryAsset";}s:2:"js";a:1:{i:0;s:6:"yii.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:19:"app\assets\AppAsset";a:9:{s:10:"sourcePath";N;s:8:"basePath";s:34:"D:/OSPanel/domains/silverzavod/web";s:7:"baseUrl";s:0:"";s:7:"depends";a:2:{i:0;s:16:"yii\web\YiiAsset";i:1;s:19:"yii\web\JqueryAsset";}s:2:"js";a:18:{i:0;s:37:"modules/bootstrap/js/bootstrap.min.js";i:1;s:35:"modules/bootstrap-datepicker.min.js";i:2;s:43:"modules/nicescroll/jquery.nicescroll.min.js";i:3;s:21:"modules/moment.min.js";i:4;s:52:"modules/bootstrap-daterangepicker/daterangepicker.js";i:5;s:12:"js/stisla.js";i:6;s:35:"modules/izitoast/js/iziToast.min.js";i:7;s:38:"modules/select2/dist/js/select2.min.js";i:8;s:34:"modules/jquery-ui/jquery-ui.min.js";i:9;s:13:"js/scripts.js";i:10;s:26:"js/jquery.inputmask.min.js";i:11;s:47:"modules/chocolat/dist/js/jquery.chocolat.min.js";i:12;s:27:"js/jquery.magnific-popup.js";i:13;s:10:"js/menu.js";i:14;s:21:"modules/moment.min.js";i:15;s:59:"modules/bootstrap-timepicker/js/bootstrap-timepicker.min.js";i:16;s:64:"//cdnjs.cloudflare.com/ajax/libs/numeral.js/2.0.6/numeral.min.js";i:17;s:21:"js/input-formatter.js";}s:3:"css";a:12:{i:0;s:12:"css/site.css";i:1;s:39:"modules/bootstrap/css/bootstrap.min.css";i:2;s:53:"modules/bootstrap-daterangepicker/daterangepicker.css";i:3;s:36:"modules/bootstrap-datepicker.min.css";i:4;s:35:"modules/fontawesome/css/all.min.css";i:5;s:37:"modules/ionicons/css/ionicons.min.css";i:6;s:40:"modules/select2/dist/css/select2.min.css";i:7;s:37:"modules/izitoast/css/iziToast.min.css";i:8;s:13:"css/style.css";i:9;s:18:"css/components.css";i:10;s:22:"css/magnific-popup.css";i:11;s:61:"modules/bootstrap-timepicker/css/bootstrap-timepicker.min.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}}";s:7:"summary";a:13:{s:3:"tag";s:13:"68724ba0e0691";s:3:"url";s:36:"http://silver/backend/expenses/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752320928.673096;s:10:"statusCode";i:200;s:8:"sqlCount";i:50;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10722216;s:14:"processingTime";d:0.40445899963378906;}s:10:"exceptions";a:0:{}}