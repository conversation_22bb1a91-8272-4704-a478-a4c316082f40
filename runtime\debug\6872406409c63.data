a:14:{s:6:"config";s:4971:"a:5:{s:10:"phpVersion";s:5:"8.1.9";s:10:"yiiVersion";s:10:"2.0.52-dev";s:11:"application";a:8:{s:3:"yii";s:10:"2.0.52-dev";s:4:"name";s:14:"My Application";s:7:"version";s:3:"1.0";s:8:"language";s:2:"uz";s:14:"sourceLanguage";s:5:"en-US";s:7:"charset";s:5:"UTF-8";s:3:"env";s:3:"dev";s:5:"debug";b:1;}s:3:"php";a:5:{s:7:"version";s:5:"8.1.9";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;s:9:"memcached";b:0;}s:10:"extensions";a:16:{s:25:"kartik-v/yii2-krajee-base";a:3:{s:4:"name";s:25:"kartik-v/yii2-krajee-base";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/base";s:67:"D:\OSPanel\domains\silverzavod\vendor/kartik-v/yii2-krajee-base/src";}}s:20:"kartik-v/yii2-dialog";a:3:{s:4:"name";s:20:"kartik-v/yii2-dialog";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:14:"@kartik/dialog";s:62:"D:\OSPanel\domains\silverzavod\vendor/kartik-v/yii2-dialog/src";}}s:18:"kartik-v/yii2-grid";a:3:{s:4:"name";s:18:"kartik-v/yii2-grid";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/grid";s:60:"D:\OSPanel\domains\silverzavod\vendor/kartik-v/yii2-grid/src";}}s:35:"kartik-v/yii2-widget-datetimepicker";a:3:{s:4:"name";s:35:"kartik-v/yii2-widget-datetimepicker";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:16:"@kartik/datetime";s:77:"D:\OSPanel\domains\silverzavod\vendor/kartik-v/yii2-widget-datetimepicker/src";}}s:16:"yiisoft/yii2-jui";a:3:{s:4:"name";s:16:"yiisoft/yii2-jui";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:8:"@yii/jui";s:58:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-jui/src";}}s:33:"2amigos/yii2-arrayquery-component";a:3:{s:4:"name";s:33:"2amigos/yii2-arrayquery-component";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:21:"@dosamigos/arrayquery";s:75:"D:\OSPanel\domains\silverzavod\vendor/2amigos/yii2-arrayquery-component/src";}}s:17:"yii2mod/yii2-rbac";a:3:{s:4:"name";s:17:"yii2mod/yii2-rbac";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:13:"@yii2mod/rbac";s:55:"D:\OSPanel\domains\silverzavod\vendor/yii2mod/yii2-rbac";}}s:23:"yiisoft/yii2-bootstrap5";a:4:{s:4:"name";s:23:"yiisoft/yii2-bootstrap5";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:15:"@yii/bootstrap5";s:65:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-bootstrap5/src";}s:9:"bootstrap";s:40:"yii\bootstrap5\i18n\TranslationBootstrap";}s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:8:"2.1.25.0";s:5:"alias";a:1:{s:10:"@yii/debug";s:60:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-debug/src";}}s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:10:"@yii/faker";s:60:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-faker/src";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.2.6.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:58:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-gii/src";}}s:18:"yiisoft/yii2-queue";a:3:{s:4:"name";s:18:"yiisoft/yii2-queue";s:7:"version";s:23:"3.0.9999999.9999999-dev";s:5:"alias";a:10:{s:10:"@yii/queue";s:60:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src";s:13:"@yii/queue/db";s:71:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/db";s:14:"@yii/queue/sqs";s:72:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/sqs";s:15:"@yii/queue/file";s:73:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/file";s:15:"@yii/queue/sync";s:73:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/sync";s:16:"@yii/queue/redis";s:74:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/redis";s:16:"@yii/queue/stomp";s:74:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/stomp";s:18:"@yii/queue/gearman";s:76:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/gearman";s:20:"@yii/queue/beanstalk";s:78:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/beanstalk";s:23:"@yii/queue/amqp_interop";s:81:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/amqp_interop";}}s:26:"yiisoft/yii2-symfonymailer";a:3:{s:4:"name";s:26:"yiisoft/yii2-symfonymailer";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:18:"@yii/symfonymailer";s:68:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-symfonymailer/src";}}s:23:"yiisoft/yii2-bootstrap4";a:3:{s:4:"name";s:23:"yiisoft/yii2-bootstrap4";s:7:"version";s:8:"2.0.11.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap4";s:65:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-bootstrap4/src";}}s:20:"yii2mod/yii2-swagger";a:3:{s:4:"name";s:20:"yii2mod/yii2-swagger";s:7:"version";s:7:"1.0.0.0";s:5:"alias";a:1:{s:16:"@yii2mod/swagger";s:58:"D:\OSPanel\domains\silverzavod\vendor/yii2mod/yii2-swagger";}}s:23:"yiisoft/yii2-httpclient";a:3:{s:4:"name";s:23:"yiisoft/yii2-httpclient";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:15:"@yii/httpclient";s:65:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-httpclient/src";}}}}";s:3:"log";s:80355:"a:1:{s:8:"messages";a:111:{i:0;a:6:{i:0;s:68:"Bootstrap with yii\bootstrap5\i18n\TranslationBootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752318051.946763;i:4;a:0:{}i:5;i:2655288;}i:1;a:6:{i:0;s:33:"Bootstrap with yii\log\Dispatcher";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752318051.947661;i:4;a:0:{}i:5;i:2773224;}i:2;a:6:{i:0;s:53:"Bootstrap with app\components\SessionTimeoutComponent";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752318051.948047;i:4;a:0:{}i:5;i:2814432;}i:3;a:6:{i:0;s:22:"Bootstrap with Closure";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752318051.948052;i:4;a:0:{}i:5;i:2814808;}i:4;a:6:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1752318051.956244;i:4;a:0:{}i:5;i:3960008;}i:5;a:6:{i:0;s:15:"Session started";i:1;i:4;i:2;s:21:"yii\web\Session::open";i:3;d:1752318051.960728;i:4;a:0:{}i:5;i:4770496;}i:6;a:6:{i:0;s:74:"Opening DB connection: pgsql:host=127.0.0.1;port=5432;dbname=silver_test_1";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:1752318051.971567;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6075144;}i:9;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.002736;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6212568;}i:12;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.017666;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6322888;}i:15;a:6:{i:0;s:63:"SELECT * FROM "users" WHERE ("id"=1) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.023957;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6617656;}i:18;a:6:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752318052.03862;i:4;a:0:{}i:5;i:7307984;}i:19;a:6:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1752318052.047805;i:4;a:0:{}i:5;i:8091896;}i:20;a:6:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752318052.04897;i:4;a:0:{}i:5;i:8116728;}i:45;a:6:{i:0;s:47:"Route requested: 'backend/worker-payment/index'";i:1;i:8;i:2;s:34:"yii\web\Application::handleRequest";i:3;d:1752318052.051366;i:4;a:0:{}i:5;i:8172696;}i:46;a:6:{i:0;s:23:"Loading module: backend";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1752318052.051383;i:4;a:0:{}i:5;i:8174328;}i:47;a:6:{i:0;s:42:"Route to run: backend/worker-payment/index";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:1752318052.056744;i:4;a:0:{}i:5;i:8303432;}i:48;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='1'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.06877;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8784008;}i:51;a:6:{i:0;s:46:"SELECT * FROM "auth_item" WHERE "name"='admin'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.071648;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8790624;}i:54;a:6:{i:0;s:20:"Checking role: admin";i:1;i:8;i:2;s:40:"yii\rbac\DbManager::checkAccessRecursive";i:3;d:1752318052.073406;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8794000;}i:55;a:6:{i:0;s:86:"Running action: app\modules\backend\controllers\WorkerPaymentController::actionIndex()";i:1;i:8;i:2;s:36:"yii\base\InlineAction::runWithParams";i:3;d:1752318052.073426;i:4;a:0:{}i:5;i:8793152;}i:56;a:6:{i:0;s:3170:"SELECT 
            w.id as worker_id,
            w.full_name,
            p.name as position,
            ws.amount as salary,
            
            -- Бонус за месяц
            COALESCE(SUM(CASE WHEN wf.type = 3 AND wf.month = '2025-07' THEN wf.amount END), 0) as bonus,
            
            -- Отпускные за месяц
            COALESCE(SUM(CASE WHEN wf.type = 7 AND wf.month = '2025-07' THEN wf.amount END), 0) as vacation_pay,
            
            -- Аванс за месяц
            COALESCE(SUM(CASE WHEN wf.type = 2 AND wf.month = '2025-07' THEN wf.amount END), 0) as advance,
            
            -- Оплата по пластиковой карте (зарплата безналичная)
            COALESCE(SUM(CASE WHEN wf.type = 1 AND wf.month = '2025-07' THEN wf.amount END), 0) as card_payment,
            
            -- Оплата наличными (зарплата наличная)
            COALESCE(SUM(CASE WHEN wf.type = 8 AND wf.month = '2025-07' THEN wf.amount END), 0) as cash_payment,
            
            -- Долг (общий долг минус все погашения)
            GREATEST(COALESCE(wd.total_debt, 0) - COALESCE(debt_payments.total_payments, 0), 0) as debt,
            
            -- Удержания долга в этом месяце
            COALESCE(SUM(CASE WHEN wf.type = 6 AND wf.month = '2025-07' THEN wf.amount END), 0) as debt_deduction,
            
            -- Получено на руки за месяц (фактически выплачено работнику)
            COALESCE(SUM(CASE WHEN wf.type = 1 AND wf.month = '2025-07' THEN wf.amount END), 0) + 
            COALESCE(SUM(CASE WHEN wf.type = 8 AND wf.month = '2025-07' THEN wf.amount END), 0) + 
            COALESCE(SUM(CASE WHEN wf.type = 3 AND wf.month = '2025-07' THEN wf.amount END), 0) + 
            COALESCE(SUM(CASE WHEN wf.type = 7 AND wf.month = '2025-07' THEN wf.amount END), 0) - 
            COALESCE(SUM(CASE WHEN wf.type = 6 AND wf.month = '2025-07' THEN wf.amount END), 0) as received_amount
            
        FROM worker w
        LEFT JOIN position p ON p.id = w.position_id
        LEFT JOIN worker_salary ws ON ws.worker_id = w.id AND ws.end_date = '9999-12-31'
        LEFT JOIN worker_finances wf ON wf.worker_id = w.id AND wf.deleted_at IS NULL
        LEFT JOIN (
            SELECT 
                worker_id,
                SUM(amount) as total_debt
            FROM worker_debt 
            WHERE deleted_at IS NULL AND (status IS NULL OR status = false)
            GROUP BY worker_id
        ) wd ON wd.worker_id = w.id
        LEFT JOIN (
            SELECT 
                worker_id,
                SUM(amount) as total_payments
            FROM worker_finances 
            WHERE type = '6' AND deleted_at IS NULL
            GROUP BY worker_id
        ) debt_payments ON debt_payments.worker_id = w.id
        WHERE w.deleted_at IS NULL
        GROUP BY w.id, w.full_name, p.name, ws.amount, wd.total_debt, debt_payments.total_payments
        ORDER BY w.full_name";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.073697;i:4;a:1:{i:0;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:90;s:8:"function";s:8:"queryAll";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}}i:5;i:8834936;}i:59;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=91) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.08283;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:8985616;}i:62;a:6:{i:0;s:2821:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'worker_finances'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.083746;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9000128;}i:65;a:6:{i:0;s:883:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='worker_finances'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.08672;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9015872;}i:68;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=87) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.088576;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9027392;}i:71;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=17) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.088885;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9031064;}i:74;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=16) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.089105;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9034088;}i:77;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=52) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.089306;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9039168;}i:80;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=59) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.08951;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9044248;}i:83;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=63) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.09022;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9055320;}i:86;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=36) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.090769;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9057416;}i:89;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=21) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.091031;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9065680;}i:92;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=41) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.091229;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9069080;}i:95;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=18) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.091416;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9074160;}i:98;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=51) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.091602;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9079240;}i:101;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=15) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.091784;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9084320;}i:104;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=70) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.091976;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9089400;}i:107;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=90) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.092161;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9094480;}i:110;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=42) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.092344;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9099560;}i:113;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=64) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.092526;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9104640;}i:116;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=75) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.092707;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9109720;}i:119;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=73) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.092888;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9120432;}i:122;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=76) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.093071;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9125512;}i:125;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=71) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.09329;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9133776;}i:128;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=72) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.093482;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9137176;}i:131;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=77) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.094053;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9146352;}i:134;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=54) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.094272;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9151432;}i:137;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=53) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.094463;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9156512;}i:140;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=74) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.094959;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9161592;}i:143;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=22) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.095253;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9166672;}i:146;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=40) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.095775;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9171752;}i:149;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=43) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.096033;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9176832;}i:152;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=65) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.096239;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9181912;}i:155;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=60) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.096429;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9186992;}i:158;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=11) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.096617;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9192072;}i:161;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=61) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.096804;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9197152;}i:164;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=79) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.096989;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9202232;}i:167;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=69) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.097171;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9207312;}i:170;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=85) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.097801;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9212392;}i:173;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=55) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.098415;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9217472;}i:176;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=24) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.098754;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9222552;}i:179;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=39) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.098968;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9227632;}i:182;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=14) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.09916;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9232712;}i:185;a:6:{i:0;s:106:"SELECT * FROM "worker_finances" WHERE (("worker_id"=8) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.099345;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9237792;}i:188;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=19) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.09953;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9242872;}i:191;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=44) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.099711;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9247952;}i:194;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=56) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.099893;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9253032;}i:197;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=31) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.100383;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9258112;}i:200;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=32) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.10081;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9263192;}i:203;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=86) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.100992;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9268272;}i:206;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=83) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.101174;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9273352;}i:209;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=88) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.101357;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9278432;}i:212;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=80) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.101599;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9283512;}i:215;a:6:{i:0;s:106:"SELECT * FROM "worker_finances" WHERE (("worker_id"=9) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.102026;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9288592;}i:218;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=66) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.102263;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9293672;}i:221;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=81) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.102506;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9298752;}i:224;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=12) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.102701;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9303832;}i:227;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=78) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.102887;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9308912;}i:230;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=49) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.103073;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9313992;}i:233;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=28) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.103513;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9319072;}i:236;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=33) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.103751;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9324152;}i:239;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=34) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.103958;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9329232;}i:242;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=27) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.104153;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9334312;}i:245;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=23) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.104341;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9339392;}i:248;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=82) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.104526;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9344472;}i:251;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=29) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.104708;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9349552;}i:254;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=67) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.104903;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9354632;}i:257;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=13) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.105086;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9367904;}i:260;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=37) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.105266;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9372984;}i:263;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=68) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.105448;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9378064;}i:266;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=25) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.105816;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9383144;}i:269;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=20) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.106098;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9388224;}i:272;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=26) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.106293;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9393304;}i:275;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=10) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.106893;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9398384;}i:278;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=57) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.107351;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9403464;}i:281;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=84) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.107536;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9408544;}i:284;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=50) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.107719;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9413624;}i:287;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=62) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.1079;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9418704;}i:290;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=35) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.108082;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9423784;}i:293;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=45) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.108264;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9428864;}i:296;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=38) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.108446;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9433944;}i:299;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=48) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.108632;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9443120;}i:302;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=47) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.10882;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9448200;}i:305;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=46) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.109264;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9453280;}i:308;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=58) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.109481;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9458360;}i:311;a:6:{i:0;s:98:"Rendering view file: D:\OSPanel\domains\silverzavod\modules\backend\views\worker-payment\index.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1752318052.111074;i:4;a:1:{i:0;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:102;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9588176;}i:312;a:6:{i:0;s:74:"Rendering view file: D:\OSPanel\domains\silverzavod\views\layouts\main.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1752318052.134271;i:4;a:1:{i:0;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:102;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10515984;}i:313;a:6:{i:0;s:74:"Rendering view file: D:\OSPanel\domains\silverzavod\views\layouts\menu.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1752318052.136158;i:4;a:2:{i:0;a:5:{s:4:"file";s:53:"D:\OSPanel\domains\silverzavod\views\layouts\main.php";s:4:"line";i:23;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:102;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10576192;}i:314;a:6:{i:0;s:89:"Rendering view file: D:\OSPanel\domains\silverzavod\views\layouts\partials/menu-admin.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1752318052.137336;i:4;a:3:{i:0;a:5:{s:4:"file";s:53:"D:\OSPanel\domains\silverzavod\views\layouts\menu.php";s:4:"line";i:54;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:53:"D:\OSPanel\domains\silverzavod\views\layouts\main.php";s:4:"line";i:23;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:102;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10615536;}i:315;a:6:{i:0;s:78:"Rendering view file: D:\OSPanel\domains\silverzavod\views\layouts\sub-menu.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1752318052.141361;i:4;a:2:{i:0;a:5:{s:4:"file";s:53:"D:\OSPanel\domains\silverzavod\views\layouts\main.php";s:4:"line";i:43;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:102;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10614960;}i:316;a:6:{i:0;s:75:"Rendering view file: D:\OSPanel\domains\silverzavod\views\layouts\modal.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1752318052.142683;i:4;a:2:{i:0;a:5:{s:4:"file";s:53:"D:\OSPanel\domains\silverzavod\views\layouts\main.php";s:4:"line";i:56;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:102;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10813024;}}}";s:9:"profiling";s:149270:"a:3:{s:6:"memory";i:11428840;s:4:"time";d:0.21056818962097168;s:8:"messages";a:182:{i:7;a:6:{i:0;s:74:"Opening DB connection: pgsql:host=127.0.0.1;port=5432;dbname=silver_test_1";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:1752318051.971618;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6076272;}i:8;a:6:{i:0;s:74:"Opening DB connection: pgsql:host=127.0.0.1;port=5432;dbname=silver_test_1";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:1752318052.001835;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6078208;}i:10;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.002767;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6214144;}i:11;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.014962;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6229504;}i:13;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.017747;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6324376;}i:14;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.01987;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6325936;}i:16;a:6:{i:0;s:63:"SELECT * FROM "users" WHERE ("id"=1) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.02403;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6619000;}i:17;a:6:{i:0;s:63:"SELECT * FROM "users" WHERE ("id"=1) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.025395;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6622008;}i:49;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='1'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.068835;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8786616;}i:50;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='1'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.071216;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8788776;}i:52;a:6:{i:0;s:46:"SELECT * FROM "auth_item" WHERE "name"='admin'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.07167;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8793232;}i:53;a:6:{i:0;s:46:"SELECT * FROM "auth_item" WHERE "name"='admin'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.07306;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8795328;}i:57;a:6:{i:0;s:3170:"SELECT 
            w.id as worker_id,
            w.full_name,
            p.name as position,
            ws.amount as salary,
            
            -- Бонус за месяц
            COALESCE(SUM(CASE WHEN wf.type = 3 AND wf.month = '2025-07' THEN wf.amount END), 0) as bonus,
            
            -- Отпускные за месяц
            COALESCE(SUM(CASE WHEN wf.type = 7 AND wf.month = '2025-07' THEN wf.amount END), 0) as vacation_pay,
            
            -- Аванс за месяц
            COALESCE(SUM(CASE WHEN wf.type = 2 AND wf.month = '2025-07' THEN wf.amount END), 0) as advance,
            
            -- Оплата по пластиковой карте (зарплата безналичная)
            COALESCE(SUM(CASE WHEN wf.type = 1 AND wf.month = '2025-07' THEN wf.amount END), 0) as card_payment,
            
            -- Оплата наличными (зарплата наличная)
            COALESCE(SUM(CASE WHEN wf.type = 8 AND wf.month = '2025-07' THEN wf.amount END), 0) as cash_payment,
            
            -- Долг (общий долг минус все погашения)
            GREATEST(COALESCE(wd.total_debt, 0) - COALESCE(debt_payments.total_payments, 0), 0) as debt,
            
            -- Удержания долга в этом месяце
            COALESCE(SUM(CASE WHEN wf.type = 6 AND wf.month = '2025-07' THEN wf.amount END), 0) as debt_deduction,
            
            -- Получено на руки за месяц (фактически выплачено работнику)
            COALESCE(SUM(CASE WHEN wf.type = 1 AND wf.month = '2025-07' THEN wf.amount END), 0) + 
            COALESCE(SUM(CASE WHEN wf.type = 8 AND wf.month = '2025-07' THEN wf.amount END), 0) + 
            COALESCE(SUM(CASE WHEN wf.type = 3 AND wf.month = '2025-07' THEN wf.amount END), 0) + 
            COALESCE(SUM(CASE WHEN wf.type = 7 AND wf.month = '2025-07' THEN wf.amount END), 0) - 
            COALESCE(SUM(CASE WHEN wf.type = 6 AND wf.month = '2025-07' THEN wf.amount END), 0) as received_amount
            
        FROM worker w
        LEFT JOIN position p ON p.id = w.position_id
        LEFT JOIN worker_salary ws ON ws.worker_id = w.id AND ws.end_date = '9999-12-31'
        LEFT JOIN worker_finances wf ON wf.worker_id = w.id AND wf.deleted_at IS NULL
        LEFT JOIN (
            SELECT 
                worker_id,
                SUM(amount) as total_debt
            FROM worker_debt 
            WHERE deleted_at IS NULL AND (status IS NULL OR status = false)
            GROUP BY worker_id
        ) wd ON wd.worker_id = w.id
        LEFT JOIN (
            SELECT 
                worker_id,
                SUM(amount) as total_payments
            FROM worker_finances 
            WHERE type = '6' AND deleted_at IS NULL
            GROUP BY worker_id
        ) debt_payments ON debt_payments.worker_id = w.id
        WHERE w.deleted_at IS NULL
        GROUP BY w.id, w.full_name, p.name, ws.amount, wd.total_debt, debt_payments.total_payments
        ORDER BY w.full_name";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.073729;i:4;a:1:{i:0;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:90;s:8:"function";s:8:"queryAll";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}}i:5;i:8842472;}i:58;a:6:{i:0;s:3170:"SELECT 
            w.id as worker_id,
            w.full_name,
            p.name as position,
            ws.amount as salary,
            
            -- Бонус за месяц
            COALESCE(SUM(CASE WHEN wf.type = 3 AND wf.month = '2025-07' THEN wf.amount END), 0) as bonus,
            
            -- Отпускные за месяц
            COALESCE(SUM(CASE WHEN wf.type = 7 AND wf.month = '2025-07' THEN wf.amount END), 0) as vacation_pay,
            
            -- Аванс за месяц
            COALESCE(SUM(CASE WHEN wf.type = 2 AND wf.month = '2025-07' THEN wf.amount END), 0) as advance,
            
            -- Оплата по пластиковой карте (зарплата безналичная)
            COALESCE(SUM(CASE WHEN wf.type = 1 AND wf.month = '2025-07' THEN wf.amount END), 0) as card_payment,
            
            -- Оплата наличными (зарплата наличная)
            COALESCE(SUM(CASE WHEN wf.type = 8 AND wf.month = '2025-07' THEN wf.amount END), 0) as cash_payment,
            
            -- Долг (общий долг минус все погашения)
            GREATEST(COALESCE(wd.total_debt, 0) - COALESCE(debt_payments.total_payments, 0), 0) as debt,
            
            -- Удержания долга в этом месяце
            COALESCE(SUM(CASE WHEN wf.type = 6 AND wf.month = '2025-07' THEN wf.amount END), 0) as debt_deduction,
            
            -- Получено на руки за месяц (фактически выплачено работнику)
            COALESCE(SUM(CASE WHEN wf.type = 1 AND wf.month = '2025-07' THEN wf.amount END), 0) + 
            COALESCE(SUM(CASE WHEN wf.type = 8 AND wf.month = '2025-07' THEN wf.amount END), 0) + 
            COALESCE(SUM(CASE WHEN wf.type = 3 AND wf.month = '2025-07' THEN wf.amount END), 0) + 
            COALESCE(SUM(CASE WHEN wf.type = 7 AND wf.month = '2025-07' THEN wf.amount END), 0) - 
            COALESCE(SUM(CASE WHEN wf.type = 6 AND wf.month = '2025-07' THEN wf.amount END), 0) as received_amount
            
        FROM worker w
        LEFT JOIN position p ON p.id = w.position_id
        LEFT JOIN worker_salary ws ON ws.worker_id = w.id AND ws.end_date = '9999-12-31'
        LEFT JOIN worker_finances wf ON wf.worker_id = w.id AND wf.deleted_at IS NULL
        LEFT JOIN (
            SELECT 
                worker_id,
                SUM(amount) as total_debt
            FROM worker_debt 
            WHERE deleted_at IS NULL AND (status IS NULL OR status = false)
            GROUP BY worker_id
        ) wd ON wd.worker_id = w.id
        LEFT JOIN (
            SELECT 
                worker_id,
                SUM(amount) as total_payments
            FROM worker_finances 
            WHERE type = '6' AND deleted_at IS NULL
            GROUP BY worker_id
        ) debt_payments ON debt_payments.worker_id = w.id
        WHERE w.deleted_at IS NULL
        GROUP BY w.id, w.full_name, p.name, ws.amount, wd.total_debt, debt_payments.total_payments
        ORDER BY w.full_name";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.081003;i:4;a:1:{i:0;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:90;s:8:"function";s:8:"queryAll";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}}i:5;i:8923304;}i:60;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=91) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.082873;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:8988704;}i:61;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=91) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.083594;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:8994672;}i:63;a:6:{i:0;s:2821:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'worker_finances'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.083769;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9001992;}i:64;a:6:{i:0;s:2821:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'worker_finances'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.086508;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9017688;}i:66;a:6:{i:0;s:883:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='worker_finances'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.086735;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9017736;}i:67;a:6:{i:0;s:883:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='worker_finances'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.08832;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9020560;}i:69;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=87) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.088587;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9030480;}i:70;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=87) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.088756;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9033976;}i:72;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=17) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.088895;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9034152;}i:73;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=17) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.089025;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9036336;}i:75;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=16) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.089113;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9037176;}i:76;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=16) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.08923;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9039360;}i:78;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=52) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.089314;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9042256;}i:79;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=52) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.089427;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9044440;}i:81;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=59) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.089519;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9047336;}i:82;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=59) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.090019;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9051824;}i:84;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=63) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.090232;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9058408;}i:85;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=63) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.090657;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9060592;}i:87;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=36) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.090779;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9060504;}i:88;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=36) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.090916;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9064000;}i:90;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=21) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.091041;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9068768;}i:91;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=21) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.091156;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9070952;}i:93;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=41) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.091236;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9072168;}i:94;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=41) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.091345;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9074352;}i:96;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=18) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.091423;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9077248;}i:97;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=18) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.091532;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9079432;}i:99;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=51) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.091609;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9082328;}i:100;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=51) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.091716;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9084512;}i:102;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=15) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.091792;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9087408;}i:103;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=15) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.091898;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9089592;}i:105;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=70) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.091984;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9092488;}i:106;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=70) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.092091;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9094672;}i:108;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=90) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.092168;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9097568;}i:109;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=90) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.092274;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9099752;}i:111;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=42) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.09235;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9102648;}i:112;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=42) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.092456;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9104832;}i:114;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=64) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.092532;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9107728;}i:115;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=64) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.092638;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9109912;}i:117;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=75) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.092714;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9112808;}i:118;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=75) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.092818;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9114992;}i:120;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=73) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.092896;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9123520;}i:121;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=73) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.093001;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9125704;}i:123;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=76) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.093077;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9128600;}i:124;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=76) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.093188;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9132096;}i:126;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=71) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.093298;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9136864;}i:127;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=71) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.093406;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9139048;}i:129;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=72) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.093492;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9144360;}i:130;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=72) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.093949;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9146544;}i:132;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=77) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.094062;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9149440;}i:133;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=77) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.094194;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9151624;}i:135;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=54) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.094281;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9154520;}i:136;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=54) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.094391;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9156704;}i:138;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=53) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.09447;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9159600;}i:139;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=53) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.094811;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9161784;}i:141;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=74) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.094972;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9164680;}i:142;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=74) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.095152;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9166864;}i:144;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=22) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.095262;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9169760;}i:145;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=22) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.095602;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9171944;}i:147;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=40) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.095787;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9174840;}i:148;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=40) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.095947;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9177024;}i:150;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=43) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.096041;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9179920;}i:151;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=43) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.096164;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9182104;}i:153;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=65) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.096246;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9185000;}i:154;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=65) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.096357;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9187184;}i:156;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=60) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.096437;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9190080;}i:157;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=60) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.096546;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9192264;}i:159;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=11) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.096625;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9195160;}i:160;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=11) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.096733;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9197344;}i:162;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=61) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.096812;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9200240;}i:163;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=61) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.096918;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9202424;}i:165;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=79) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.096996;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9205320;}i:166;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=79) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.097102;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9207504;}i:168;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=69) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.097177;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9210400;}i:169;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=69) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.097482;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9212584;}i:171;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=85) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.097848;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9215480;}i:172;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=85) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.098249;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9217664;}i:174;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=55) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.098431;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9220560;}i:175;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=55) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.098651;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9222744;}i:177;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=24) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.098764;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9225640;}i:178;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=24) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.098892;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9227824;}i:180;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=39) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.098976;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9230720;}i:181;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=39) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.09909;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9232904;}i:183;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=14) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.099167;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9235800;}i:184;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=14) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.099276;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9237984;}i:186;a:6:{i:0;s:106:"SELECT * FROM "worker_finances" WHERE (("worker_id"=8) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.099352;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9240880;}i:187;a:6:{i:0;s:106:"SELECT * FROM "worker_finances" WHERE (("worker_id"=8) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.099459;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9243032;}i:189;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=19) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.099536;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9245960;}i:190;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=19) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.099643;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9248144;}i:192;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=44) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.099718;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9251040;}i:193;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=44) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.099824;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9253224;}i:195;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=56) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.0999;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9256120;}i:196;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=56) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.100006;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9258304;}i:198;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=31) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.100438;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9261200;}i:199;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=31) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.100738;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9263384;}i:201;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=32) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.100816;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9266280;}i:202;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=32) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.100922;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9268464;}i:204;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=86) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.100999;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9271360;}i:205;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=86) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.101104;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9273544;}i:207;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=83) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.101181;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9276440;}i:208;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=83) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.101287;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9278624;}i:210;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=88) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.101364;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9281520;}i:211;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=88) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.10147;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9283704;}i:213;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=80) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.101615;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9286600;}i:214;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=80) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.101887;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9288784;}i:216;a:6:{i:0;s:106:"SELECT * FROM "worker_finances" WHERE (("worker_id"=9) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.102038;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9291680;}i:217;a:6:{i:0;s:106:"SELECT * FROM "worker_finances" WHERE (("worker_id"=9) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.102177;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9293832;}i:219;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=66) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.102273;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9296760;}i:220;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=66) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.10243;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9298944;}i:222;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=81) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.102514;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9301840;}i:223;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=81) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.10263;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9304024;}i:225;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=12) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.102708;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9306920;}i:226;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=12) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.102817;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9309104;}i:228;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=78) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.102894;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9312000;}i:229;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=78) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.103002;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9314184;}i:231;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=49) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.10308;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9317080;}i:232;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=49) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.103392;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9319264;}i:234;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=28) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.103524;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9322160;}i:235;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=28) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.103664;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9324344;}i:237;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=33) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.10376;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9327240;}i:238;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=33) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.10388;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9329424;}i:240;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=34) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.103966;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9332320;}i:241;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=34) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.104079;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9334504;}i:243;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=27) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.104161;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9337400;}i:244;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=27) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.10427;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9339584;}i:246;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=23) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.104349;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9342480;}i:247;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=23) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.104456;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9344664;}i:249;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=82) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.104532;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9347560;}i:250;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=82) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.104639;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9349744;}i:252;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=29) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.104716;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9352640;}i:253;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=29) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.104832;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9354824;}i:255;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=67) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.104909;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9357720;}i:256;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=67) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.105015;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9359904;}i:258;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=13) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.105093;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9370992;}i:259;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=13) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.105198;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9373176;}i:261;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=37) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.105273;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9376072;}i:262;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=37) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.105378;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9378256;}i:264;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=68) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.105454;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9381152;}i:265;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=68) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.105577;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9383336;}i:267;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=25) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.105834;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9386232;}i:268;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=25) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.106008;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9388416;}i:270;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=20) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.106107;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9391312;}i:271;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=20) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.106221;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9393496;}i:273;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=26) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.106301;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9396392;}i:274;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=26) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.106524;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9398576;}i:276;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=10) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.106943;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9401472;}i:277;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=10) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.107279;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9403656;}i:279;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=57) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.107358;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9406552;}i:280;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=57) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.107465;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9408736;}i:282;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=84) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.107544;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9411632;}i:283;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=84) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.107649;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9413816;}i:285;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=50) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.107726;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9416712;}i:286;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=50) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.107831;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9418896;}i:288;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=62) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.107907;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9421792;}i:289;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=62) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.108012;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9423976;}i:291;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=35) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.108089;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9426872;}i:292;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=35) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.108195;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9429056;}i:294;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=45) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.108271;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9431952;}i:295;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=45) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.108376;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9434136;}i:297;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=38) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.108454;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9437032;}i:298;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=38) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.10856;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9439216;}i:300;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=48) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.108638;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9446208;}i:301;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=48) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.108743;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9448392;}i:303;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=47) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.108828;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9451288;}i:304;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=47) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.109184;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9453472;}i:306;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=46) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.109272;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9456368;}i:307;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=46) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.109408;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9458552;}i:309;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=58) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.109489;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9461448;}i:310;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=58) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.109744;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9463632;}}}";s:2:"db";s:148435:"a:1:{s:8:"messages";a:180:{i:10;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.002767;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6214144;}i:11;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.014962;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6229504;}i:13;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.017747;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6324376;}i:14;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.01987;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6325936;}i:16;a:6:{i:0;s:63:"SELECT * FROM "users" WHERE ("id"=1) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.02403;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6619000;}i:17;a:6:{i:0;s:63:"SELECT * FROM "users" WHERE ("id"=1) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.025395;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6622008;}i:49;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='1'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.068835;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8786616;}i:50;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='1'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.071216;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8788776;}i:52;a:6:{i:0;s:46:"SELECT * FROM "auth_item" WHERE "name"='admin'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.07167;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8793232;}i:53;a:6:{i:0;s:46:"SELECT * FROM "auth_item" WHERE "name"='admin'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.07306;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8795328;}i:57;a:6:{i:0;s:3170:"SELECT 
            w.id as worker_id,
            w.full_name,
            p.name as position,
            ws.amount as salary,
            
            -- Бонус за месяц
            COALESCE(SUM(CASE WHEN wf.type = 3 AND wf.month = '2025-07' THEN wf.amount END), 0) as bonus,
            
            -- Отпускные за месяц
            COALESCE(SUM(CASE WHEN wf.type = 7 AND wf.month = '2025-07' THEN wf.amount END), 0) as vacation_pay,
            
            -- Аванс за месяц
            COALESCE(SUM(CASE WHEN wf.type = 2 AND wf.month = '2025-07' THEN wf.amount END), 0) as advance,
            
            -- Оплата по пластиковой карте (зарплата безналичная)
            COALESCE(SUM(CASE WHEN wf.type = 1 AND wf.month = '2025-07' THEN wf.amount END), 0) as card_payment,
            
            -- Оплата наличными (зарплата наличная)
            COALESCE(SUM(CASE WHEN wf.type = 8 AND wf.month = '2025-07' THEN wf.amount END), 0) as cash_payment,
            
            -- Долг (общий долг минус все погашения)
            GREATEST(COALESCE(wd.total_debt, 0) - COALESCE(debt_payments.total_payments, 0), 0) as debt,
            
            -- Удержания долга в этом месяце
            COALESCE(SUM(CASE WHEN wf.type = 6 AND wf.month = '2025-07' THEN wf.amount END), 0) as debt_deduction,
            
            -- Получено на руки за месяц (фактически выплачено работнику)
            COALESCE(SUM(CASE WHEN wf.type = 1 AND wf.month = '2025-07' THEN wf.amount END), 0) + 
            COALESCE(SUM(CASE WHEN wf.type = 8 AND wf.month = '2025-07' THEN wf.amount END), 0) + 
            COALESCE(SUM(CASE WHEN wf.type = 3 AND wf.month = '2025-07' THEN wf.amount END), 0) + 
            COALESCE(SUM(CASE WHEN wf.type = 7 AND wf.month = '2025-07' THEN wf.amount END), 0) - 
            COALESCE(SUM(CASE WHEN wf.type = 6 AND wf.month = '2025-07' THEN wf.amount END), 0) as received_amount
            
        FROM worker w
        LEFT JOIN position p ON p.id = w.position_id
        LEFT JOIN worker_salary ws ON ws.worker_id = w.id AND ws.end_date = '9999-12-31'
        LEFT JOIN worker_finances wf ON wf.worker_id = w.id AND wf.deleted_at IS NULL
        LEFT JOIN (
            SELECT 
                worker_id,
                SUM(amount) as total_debt
            FROM worker_debt 
            WHERE deleted_at IS NULL AND (status IS NULL OR status = false)
            GROUP BY worker_id
        ) wd ON wd.worker_id = w.id
        LEFT JOIN (
            SELECT 
                worker_id,
                SUM(amount) as total_payments
            FROM worker_finances 
            WHERE type = '6' AND deleted_at IS NULL
            GROUP BY worker_id
        ) debt_payments ON debt_payments.worker_id = w.id
        WHERE w.deleted_at IS NULL
        GROUP BY w.id, w.full_name, p.name, ws.amount, wd.total_debt, debt_payments.total_payments
        ORDER BY w.full_name";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.073729;i:4;a:1:{i:0;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:90;s:8:"function";s:8:"queryAll";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}}i:5;i:8842472;}i:58;a:6:{i:0;s:3170:"SELECT 
            w.id as worker_id,
            w.full_name,
            p.name as position,
            ws.amount as salary,
            
            -- Бонус за месяц
            COALESCE(SUM(CASE WHEN wf.type = 3 AND wf.month = '2025-07' THEN wf.amount END), 0) as bonus,
            
            -- Отпускные за месяц
            COALESCE(SUM(CASE WHEN wf.type = 7 AND wf.month = '2025-07' THEN wf.amount END), 0) as vacation_pay,
            
            -- Аванс за месяц
            COALESCE(SUM(CASE WHEN wf.type = 2 AND wf.month = '2025-07' THEN wf.amount END), 0) as advance,
            
            -- Оплата по пластиковой карте (зарплата безналичная)
            COALESCE(SUM(CASE WHEN wf.type = 1 AND wf.month = '2025-07' THEN wf.amount END), 0) as card_payment,
            
            -- Оплата наличными (зарплата наличная)
            COALESCE(SUM(CASE WHEN wf.type = 8 AND wf.month = '2025-07' THEN wf.amount END), 0) as cash_payment,
            
            -- Долг (общий долг минус все погашения)
            GREATEST(COALESCE(wd.total_debt, 0) - COALESCE(debt_payments.total_payments, 0), 0) as debt,
            
            -- Удержания долга в этом месяце
            COALESCE(SUM(CASE WHEN wf.type = 6 AND wf.month = '2025-07' THEN wf.amount END), 0) as debt_deduction,
            
            -- Получено на руки за месяц (фактически выплачено работнику)
            COALESCE(SUM(CASE WHEN wf.type = 1 AND wf.month = '2025-07' THEN wf.amount END), 0) + 
            COALESCE(SUM(CASE WHEN wf.type = 8 AND wf.month = '2025-07' THEN wf.amount END), 0) + 
            COALESCE(SUM(CASE WHEN wf.type = 3 AND wf.month = '2025-07' THEN wf.amount END), 0) + 
            COALESCE(SUM(CASE WHEN wf.type = 7 AND wf.month = '2025-07' THEN wf.amount END), 0) - 
            COALESCE(SUM(CASE WHEN wf.type = 6 AND wf.month = '2025-07' THEN wf.amount END), 0) as received_amount
            
        FROM worker w
        LEFT JOIN position p ON p.id = w.position_id
        LEFT JOIN worker_salary ws ON ws.worker_id = w.id AND ws.end_date = '9999-12-31'
        LEFT JOIN worker_finances wf ON wf.worker_id = w.id AND wf.deleted_at IS NULL
        LEFT JOIN (
            SELECT 
                worker_id,
                SUM(amount) as total_debt
            FROM worker_debt 
            WHERE deleted_at IS NULL AND (status IS NULL OR status = false)
            GROUP BY worker_id
        ) wd ON wd.worker_id = w.id
        LEFT JOIN (
            SELECT 
                worker_id,
                SUM(amount) as total_payments
            FROM worker_finances 
            WHERE type = '6' AND deleted_at IS NULL
            GROUP BY worker_id
        ) debt_payments ON debt_payments.worker_id = w.id
        WHERE w.deleted_at IS NULL
        GROUP BY w.id, w.full_name, p.name, ws.amount, wd.total_debt, debt_payments.total_payments
        ORDER BY w.full_name";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.081003;i:4;a:1:{i:0;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:90;s:8:"function";s:8:"queryAll";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}}i:5;i:8923304;}i:60;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=91) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.082873;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:8988704;}i:61;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=91) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.083594;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:8994672;}i:63;a:6:{i:0;s:2821:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'worker_finances'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.083769;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9001992;}i:64;a:6:{i:0;s:2821:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'worker_finances'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.086508;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9017688;}i:66;a:6:{i:0;s:883:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='worker_finances'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.086735;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9017736;}i:67;a:6:{i:0;s:883:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='worker_finances'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.08832;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9020560;}i:69;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=87) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.088587;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9030480;}i:70;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=87) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.088756;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9033976;}i:72;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=17) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.088895;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9034152;}i:73;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=17) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.089025;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9036336;}i:75;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=16) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.089113;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9037176;}i:76;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=16) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.08923;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9039360;}i:78;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=52) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.089314;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9042256;}i:79;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=52) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.089427;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9044440;}i:81;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=59) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.089519;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9047336;}i:82;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=59) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.090019;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9051824;}i:84;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=63) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.090232;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9058408;}i:85;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=63) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.090657;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9060592;}i:87;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=36) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.090779;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9060504;}i:88;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=36) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.090916;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9064000;}i:90;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=21) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.091041;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9068768;}i:91;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=21) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.091156;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9070952;}i:93;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=41) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.091236;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9072168;}i:94;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=41) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.091345;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9074352;}i:96;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=18) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.091423;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9077248;}i:97;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=18) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.091532;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9079432;}i:99;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=51) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.091609;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9082328;}i:100;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=51) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.091716;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9084512;}i:102;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=15) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.091792;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9087408;}i:103;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=15) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.091898;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9089592;}i:105;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=70) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.091984;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9092488;}i:106;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=70) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.092091;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9094672;}i:108;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=90) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.092168;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9097568;}i:109;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=90) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.092274;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9099752;}i:111;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=42) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.09235;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9102648;}i:112;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=42) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.092456;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9104832;}i:114;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=64) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.092532;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9107728;}i:115;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=64) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.092638;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9109912;}i:117;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=75) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.092714;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9112808;}i:118;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=75) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.092818;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9114992;}i:120;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=73) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.092896;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9123520;}i:121;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=73) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.093001;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9125704;}i:123;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=76) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.093077;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9128600;}i:124;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=76) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.093188;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9132096;}i:126;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=71) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.093298;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9136864;}i:127;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=71) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.093406;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9139048;}i:129;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=72) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.093492;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9144360;}i:130;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=72) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.093949;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9146544;}i:132;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=77) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.094062;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9149440;}i:133;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=77) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.094194;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9151624;}i:135;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=54) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.094281;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9154520;}i:136;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=54) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.094391;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9156704;}i:138;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=53) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.09447;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9159600;}i:139;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=53) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.094811;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9161784;}i:141;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=74) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.094972;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9164680;}i:142;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=74) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.095152;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9166864;}i:144;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=22) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.095262;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9169760;}i:145;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=22) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.095602;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9171944;}i:147;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=40) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.095787;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9174840;}i:148;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=40) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.095947;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9177024;}i:150;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=43) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.096041;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9179920;}i:151;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=43) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.096164;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9182104;}i:153;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=65) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.096246;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9185000;}i:154;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=65) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.096357;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9187184;}i:156;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=60) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.096437;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9190080;}i:157;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=60) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.096546;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9192264;}i:159;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=11) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.096625;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9195160;}i:160;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=11) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.096733;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9197344;}i:162;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=61) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.096812;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9200240;}i:163;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=61) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.096918;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9202424;}i:165;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=79) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.096996;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9205320;}i:166;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=79) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.097102;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9207504;}i:168;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=69) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.097177;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9210400;}i:169;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=69) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.097482;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9212584;}i:171;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=85) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.097848;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9215480;}i:172;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=85) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.098249;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9217664;}i:174;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=55) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.098431;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9220560;}i:175;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=55) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.098651;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9222744;}i:177;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=24) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.098764;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9225640;}i:178;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=24) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.098892;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9227824;}i:180;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=39) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.098976;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9230720;}i:181;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=39) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.09909;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9232904;}i:183;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=14) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.099167;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9235800;}i:184;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=14) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.099276;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9237984;}i:186;a:6:{i:0;s:106:"SELECT * FROM "worker_finances" WHERE (("worker_id"=8) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.099352;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9240880;}i:187;a:6:{i:0;s:106:"SELECT * FROM "worker_finances" WHERE (("worker_id"=8) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.099459;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9243032;}i:189;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=19) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.099536;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9245960;}i:190;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=19) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.099643;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9248144;}i:192;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=44) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.099718;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9251040;}i:193;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=44) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.099824;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9253224;}i:195;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=56) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.0999;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9256120;}i:196;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=56) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.100006;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9258304;}i:198;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=31) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.100438;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9261200;}i:199;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=31) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.100738;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9263384;}i:201;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=32) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.100816;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9266280;}i:202;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=32) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.100922;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9268464;}i:204;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=86) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.100999;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9271360;}i:205;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=86) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.101104;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9273544;}i:207;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=83) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.101181;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9276440;}i:208;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=83) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.101287;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9278624;}i:210;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=88) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.101364;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9281520;}i:211;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=88) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.10147;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9283704;}i:213;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=80) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.101615;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9286600;}i:214;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=80) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.101887;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9288784;}i:216;a:6:{i:0;s:106:"SELECT * FROM "worker_finances" WHERE (("worker_id"=9) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.102038;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9291680;}i:217;a:6:{i:0;s:106:"SELECT * FROM "worker_finances" WHERE (("worker_id"=9) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.102177;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9293832;}i:219;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=66) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.102273;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9296760;}i:220;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=66) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.10243;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9298944;}i:222;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=81) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.102514;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9301840;}i:223;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=81) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.10263;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9304024;}i:225;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=12) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.102708;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9306920;}i:226;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=12) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.102817;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9309104;}i:228;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=78) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.102894;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9312000;}i:229;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=78) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.103002;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9314184;}i:231;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=49) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.10308;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9317080;}i:232;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=49) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.103392;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9319264;}i:234;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=28) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.103524;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9322160;}i:235;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=28) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.103664;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9324344;}i:237;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=33) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.10376;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9327240;}i:238;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=33) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.10388;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9329424;}i:240;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=34) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.103966;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9332320;}i:241;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=34) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.104079;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9334504;}i:243;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=27) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.104161;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9337400;}i:244;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=27) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.10427;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9339584;}i:246;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=23) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.104349;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9342480;}i:247;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=23) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.104456;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9344664;}i:249;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=82) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.104532;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9347560;}i:250;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=82) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.104639;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9349744;}i:252;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=29) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.104716;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9352640;}i:253;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=29) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.104832;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9354824;}i:255;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=67) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.104909;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9357720;}i:256;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=67) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.105015;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9359904;}i:258;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=13) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.105093;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9370992;}i:259;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=13) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.105198;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9373176;}i:261;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=37) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.105273;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9376072;}i:262;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=37) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.105378;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9378256;}i:264;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=68) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.105454;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9381152;}i:265;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=68) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.105577;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9383336;}i:267;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=25) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.105834;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9386232;}i:268;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=25) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.106008;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9388416;}i:270;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=20) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.106107;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9391312;}i:271;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=20) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.106221;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9393496;}i:273;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=26) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.106301;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9396392;}i:274;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=26) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.106524;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9398576;}i:276;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=10) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.106943;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9401472;}i:277;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=10) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.107279;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9403656;}i:279;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=57) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.107358;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9406552;}i:280;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=57) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.107465;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9408736;}i:282;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=84) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.107544;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9411632;}i:283;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=84) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.107649;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9413816;}i:285;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=50) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.107726;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9416712;}i:286;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=50) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.107831;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9418896;}i:288;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=62) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.107907;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9421792;}i:289;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=62) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.108012;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9423976;}i:291;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=35) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.108089;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9426872;}i:292;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=35) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.108195;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9429056;}i:294;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=45) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.108271;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9431952;}i:295;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=45) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.108376;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9434136;}i:297;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=38) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.108454;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9437032;}i:298;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=38) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.10856;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9439216;}i:300;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=48) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.108638;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9446208;}i:301;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=48) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.108743;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9448392;}i:303;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=47) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.108828;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9451288;}i:304;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=47) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.109184;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9453472;}i:306;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=46) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.109272;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9456368;}i:307;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=46) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.109408;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9458552;}i:309;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=58) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.109489;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9461448;}i:310;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=58) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752318052.109744;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9463632;}}}";s:5:"event";s:27985:"a:159:{i:0;a:5:{s:4:"time";d:1752318051.965448;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:1;a:5:{s:4:"time";d:1752318052.001825;s:4:"name";s:9:"afterOpen";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:2;a:5:{s:4:"time";d:1752318052.025495;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:32:"app\modules\backend\models\Users";}i:3;a:5:{s:4:"time";d:1752318052.025532;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:32:"app\modules\backend\models\Users";}i:4;a:5:{s:4:"time";d:1752318052.032284;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:32:"app\modules\backend\models\Users";}i:5;a:5:{s:4:"time";d:1752318052.049904;s:4:"name";s:13:"beforeRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:6;a:5:{s:4:"time";d:1752318052.057172;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:7;a:5:{s:4:"time";d:1752318052.0572;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"app\modules\backend\BackendModule";}i:8;a:5:{s:4:"time";d:1752318052.063465;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:9;a:5:{s:4:"time";d:1752318052.063502;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:10;a:5:{s:4:"time";d:1752318052.063522;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:11;a:5:{s:4:"time";d:1752318052.063539;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:12;a:5:{s:4:"time";d:1752318052.063555;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:13;a:5:{s:4:"time";d:1752318052.063568;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:14;a:5:{s:4:"time";d:1752318052.063592;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:15;a:5:{s:4:"time";d:1752318052.063666;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:55:"app\modules\backend\controllers\WorkerPaymentController";}i:16;a:5:{s:4:"time";d:1752318052.081828;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:17;a:5:{s:4:"time";d:1752318052.083689;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:18;a:5:{s:4:"time";d:1752318052.088438;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:19;a:5:{s:4:"time";d:1752318052.088449;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:20;a:5:{s:4:"time";d:1752318052.088456;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:21;a:5:{s:4:"time";d:1752318052.088462;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:22;a:5:{s:4:"time";d:1752318052.088468;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:23;a:5:{s:4:"time";d:1752318052.088474;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:24;a:5:{s:4:"time";d:1752318052.088481;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:25;a:5:{s:4:"time";d:1752318052.088484;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:26;a:5:{s:4:"time";d:1752318052.088486;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:27;a:5:{s:4:"time";d:1752318052.088488;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:28;a:5:{s:4:"time";d:1752318052.08849;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:29;a:5:{s:4:"time";d:1752318052.088493;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:30;a:5:{s:4:"time";d:1752318052.088495;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:31;a:5:{s:4:"time";d:1752318052.088519;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:32;a:5:{s:4:"time";d:1752318052.088804;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:33;a:5:{s:4:"time";d:1752318052.088819;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:34;a:5:{s:4:"time";d:1752318052.088826;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:35;a:5:{s:4:"time";d:1752318052.088829;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:36;a:5:{s:4:"time";d:1752318052.088843;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:37;a:5:{s:4:"time";d:1752318052.089072;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:38;a:5:{s:4:"time";d:1752318052.089275;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:39;a:5:{s:4:"time";d:1752318052.089472;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:40;a:5:{s:4:"time";d:1752318052.090101;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:41;a:5:{s:4:"time";d:1752318052.090122;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:42;a:5:{s:4:"time";d:1752318052.09013;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:43;a:5:{s:4:"time";d:1752318052.090137;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:44;a:5:{s:4:"time";d:1752318052.090143;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:45;a:5:{s:4:"time";d:1752318052.090146;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:46;a:5:{s:4:"time";d:1752318052.090148;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:47;a:5:{s:4:"time";d:1752318052.09015;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:48;a:5:{s:4:"time";d:1752318052.090169;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:49;a:5:{s:4:"time";d:1752318052.090725;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:50;a:5:{s:4:"time";d:1752318052.09096;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:51;a:5:{s:4:"time";d:1752318052.090976;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:52;a:5:{s:4:"time";d:1752318052.090984;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:53;a:5:{s:4:"time";d:1752318052.090986;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:54;a:5:{s:4:"time";d:1752318052.091;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:55;a:5:{s:4:"time";d:1752318052.0912;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:56;a:5:{s:4:"time";d:1752318052.091388;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:57;a:5:{s:4:"time";d:1752318052.091574;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:58;a:5:{s:4:"time";d:1752318052.091758;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:59;a:5:{s:4:"time";d:1752318052.09195;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:60;a:5:{s:4:"time";d:1752318052.092134;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:61;a:5:{s:4:"time";d:1752318052.092316;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:62;a:5:{s:4:"time";d:1752318052.092498;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:63;a:5:{s:4:"time";d:1752318052.09268;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:64;a:5:{s:4:"time";d:1752318052.092861;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:65;a:5:{s:4:"time";d:1752318052.093044;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:66;a:5:{s:4:"time";d:1752318052.093228;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:67;a:5:{s:4:"time";d:1752318052.093241;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:68;a:5:{s:4:"time";d:1752318052.093248;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:69;a:5:{s:4:"time";d:1752318052.093251;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:70;a:5:{s:4:"time";d:1752318052.093262;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:71;a:5:{s:4:"time";d:1752318052.093449;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:72;a:5:{s:4:"time";d:1752318052.094018;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:73;a:5:{s:4:"time";d:1752318052.09424;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:74;a:5:{s:4:"time";d:1752318052.094434;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:75;a:5:{s:4:"time";d:1752318052.094906;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:76;a:5:{s:4:"time";d:1752318052.095211;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:77;a:5:{s:4:"time";d:1752318052.095723;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:78;a:5:{s:4:"time";d:1752318052.095997;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:79;a:5:{s:4:"time";d:1752318052.096208;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:80;a:5:{s:4:"time";d:1752318052.096401;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:81;a:5:{s:4:"time";d:1752318052.096589;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:82;a:5:{s:4:"time";d:1752318052.096776;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:83;a:5:{s:4:"time";d:1752318052.096962;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:84;a:5:{s:4:"time";d:1752318052.097144;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:85;a:5:{s:4:"time";d:1752318052.097608;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:86;a:5:{s:4:"time";d:1752318052.098342;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:87;a:5:{s:4:"time";d:1752318052.098711;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:88;a:5:{s:4:"time";d:1752318052.098938;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:89;a:5:{s:4:"time";d:1752318052.099133;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:90;a:5:{s:4:"time";d:1752318052.099318;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:91;a:5:{s:4:"time";d:1752318052.099502;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:92;a:5:{s:4:"time";d:1752318052.099685;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:93;a:5:{s:4:"time";d:1752318052.099866;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:94;a:5:{s:4:"time";d:1752318052.100195;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:95;a:5:{s:4:"time";d:1752318052.100782;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:96;a:5:{s:4:"time";d:1752318052.100964;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:97;a:5:{s:4:"time";d:1752318052.101147;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:98;a:5:{s:4:"time";d:1752318052.10133;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:99;a:5:{s:4:"time";d:1752318052.101539;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:100;a:5:{s:4:"time";d:1752318052.101973;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:101;a:5:{s:4:"time";d:1752318052.102225;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:102;a:5:{s:4:"time";d:1752318052.102475;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:103;a:5:{s:4:"time";d:1752318052.102672;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:104;a:5:{s:4:"time";d:1752318052.102859;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:105;a:5:{s:4:"time";d:1752318052.103045;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:106;a:5:{s:4:"time";d:1752318052.103463;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:107;a:5:{s:4:"time";d:1752318052.103713;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:108;a:5:{s:4:"time";d:1752318052.103925;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:109;a:5:{s:4:"time";d:1752318052.104122;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:110;a:5:{s:4:"time";d:1752318052.104313;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:111;a:5:{s:4:"time";d:1752318052.104498;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:112;a:5:{s:4:"time";d:1752318052.104681;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:113;a:5:{s:4:"time";d:1752318052.104875;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:114;a:5:{s:4:"time";d:1752318052.105059;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:115;a:5:{s:4:"time";d:1752318052.10524;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:116;a:5:{s:4:"time";d:1752318052.10542;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:117;a:5:{s:4:"time";d:1752318052.105752;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:118;a:5:{s:4:"time";d:1752318052.106061;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:119;a:5:{s:4:"time";d:1752318052.106264;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:120;a:5:{s:4:"time";d:1752318052.106705;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:121;a:5:{s:4:"time";d:1752318052.107322;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:122;a:5:{s:4:"time";d:1752318052.107508;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:123;a:5:{s:4:"time";d:1752318052.107692;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:124;a:5:{s:4:"time";d:1752318052.107873;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:125;a:5:{s:4:"time";d:1752318052.108054;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:126;a:5:{s:4:"time";d:1752318052.108237;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:127;a:5:{s:4:"time";d:1752318052.108418;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:128;a:5:{s:4:"time";d:1752318052.108603;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:129;a:5:{s:4:"time";d:1752318052.108789;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:130;a:5:{s:4:"time";d:1752318052.109236;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:131;a:5:{s:4:"time";d:1752318052.109452;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:132;a:5:{s:4:"time";d:1752318052.111067;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:133;a:5:{s:4:"time";d:1752318052.124902;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:134;a:5:{s:4:"time";d:1752318052.126742;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\widgets\Pjax";}i:135;a:5:{s:4:"time";d:1752318052.128836;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\widgets\Pjax";}i:136;a:5:{s:4:"time";d:1752318052.13319;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\widgets\Pjax";}i:137;a:5:{s:4:"time";d:1752318052.133742;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:138;a:5:{s:4:"time";d:1752318052.134266;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:139;a:5:{s:4:"time";d:1752318052.134785;s:4:"name";s:9:"beginPage";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:140;a:5:{s:4:"time";d:1752318052.135668;s:4:"name";s:9:"beginBody";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:141;a:5:{s:4:"time";d:1752318052.136146;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:142;a:5:{s:4:"time";d:1752318052.137321;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:143;a:5:{s:4:"time";d:1752318052.140572;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:144;a:5:{s:4:"time";d:1752318052.140845;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:145;a:5:{s:4:"time";d:1752318052.141349;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:146;a:5:{s:4:"time";d:1752318052.142129;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:147;a:5:{s:4:"time";d:1752318052.142669;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:148;a:5:{s:4:"time";d:1752318052.143149;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:149;a:5:{s:4:"time";d:1752318052.144732;s:4:"name";s:7:"endBody";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:150;a:5:{s:4:"time";d:1752318052.145735;s:4:"name";s:7:"endPage";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:151;a:5:{s:4:"time";d:1752318052.148824;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:152;a:5:{s:4:"time";d:1752318052.149418;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:55:"app\modules\backend\controllers\WorkerPaymentController";}i:153;a:5:{s:4:"time";d:1752318052.149427;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"app\modules\backend\BackendModule";}i:154;a:5:{s:4:"time";d:1752318052.149431;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:155;a:5:{s:4:"time";d:1752318052.149437;s:4:"name";s:12:"afterRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:156;a:5:{s:4:"time";d:1752318052.149441;s:4:"name";s:10:"beforeSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:157;a:5:{s:4:"time";d:1752318052.149798;s:4:"name";s:12:"afterPrepare";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:158;a:5:{s:4:"time";d:1752318052.150071;s:4:"name";s:9:"afterSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}}";s:4:"mail";s:6:"a:0:{}";s:8:"timeline";s:91:"a:3:{s:5:"start";d:1752318051.940343;s:3:"end";d:1752318052.15355;s:6:"memory";i:11428840;}";s:4:"dump";s:6:"a:0:{}";s:6:"router";s:4871:"a:3:{s:8:"messages";a:24:{i:21;a:6:{i:0;a:3:{s:4:"rule";s:3:"gii";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752318052.050976;i:4;a:0:{}i:5;i:8154064;}i:22;a:6:{i:0;a:3:{s:4:"rule";s:12:"gii/<id:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752318052.051018;i:4;a:0:{}i:5;i:8154816;}i:23;a:6:{i:0;a:3:{s:4:"rule";s:41:"gii/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752318052.051041;i:4;a:0:{}i:5;i:8155568;}i:24;a:6:{i:0;a:3:{s:4:"rule";s:5:"debug";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752318052.051056;i:4;a:0:{}i:5;i:8156320;}i:25;a:6:{i:0;a:3:{s:4:"rule";s:43:"debug/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752318052.051071;i:4;a:0:{}i:5;i:8157072;}i:26;a:6:{i:0;a:3:{s:4:"rule";s:1:"/";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752318052.051087;i:4;a:0:{}i:5;i:8157824;}i:27;a:6:{i:0;a:3:{s:4:"rule";s:5:"login";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752318052.051101;i:4;a:0:{}i:5;i:8158576;}i:28;a:6:{i:0;a:3:{s:4:"rule";s:6:"logout";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752318052.051115;i:4;a:0:{}i:5;i:8159328;}i:29;a:6:{i:0;a:3:{s:4:"rule";s:13:"site/<action>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752318052.051131;i:4;a:0:{}i:5;i:8160080;}i:30;a:6:{i:0;a:3:{s:4:"rule";s:39:"api/<controller:[\w-]+>/<action:[\w-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752318052.051146;i:4;a:0:{}i:5;i:8160832;}i:31;a:6:{i:0;a:3:{s:4:"rule";s:23:"api/<controller:[\w-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752318052.051161;i:4;a:0:{}i:5;i:8161584;}i:32;a:6:{i:0;a:3:{s:4:"rule";s:12:"swagger/json";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752318052.051175;i:4;a:0:{}i:5;i:8162336;}i:33;a:6:{i:0;a:3:{s:4:"rule";s:10:"swagger/ui";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752318052.051189;i:4;a:0:{}i:5;i:8164368;}i:34;a:6:{i:0;a:3:{s:4:"rule";s:7:"swagger";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752318052.0512;i:4;a:0:{}i:5;i:8165120;}i:35;a:6:{i:0;a:3:{s:4:"rule";s:25:"backend/position/<action>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752318052.051214;i:4;a:0:{}i:5;i:8165872;}i:36;a:6:{i:0;a:3:{s:4:"rule";s:16:"backend/position";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752318052.05123;i:4;a:0:{}i:5;i:8166624;}i:37;a:6:{i:0;a:3:{s:4:"rule";s:32:"backend/equipment/<id:\d+>/parts";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752318052.051243;i:4;a:0:{}i:5;i:8167376;}i:38;a:6:{i:0;a:3:{s:4:"rule";s:60:"backend/equipment/defect-part/<equipmentId:\d+>/<partId:\d+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752318052.051255;i:4;a:0:{}i:5;i:8168128;}i:39;a:6:{i:0;a:3:{s:4:"rule";s:61:"backend/equipment/reserve-part/<equipmentId:\d+>/<partId:\d+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752318052.051267;i:4;a:0:{}i:5;i:8168880;}i:40;a:6:{i:0;a:3:{s:4:"rule";s:60:"backend/equipment/repair-part/<equipmentId:\d+>/<partId:\d+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752318052.051279;i:4;a:0:{}i:5;i:8169632;}i:41;a:6:{i:0;a:3:{s:4:"rule";s:62:"backend/equipment/activate-part/<equipmentId:\d+>/<partId:\d+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752318052.05129;i:4;a:0:{}i:5;i:8170384;}i:42;a:6:{i:0;a:3:{s:4:"rule";s:38:"backend/<controller>/<action>/<id:\d+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752318052.051302;i:4;a:0:{}i:5;i:8171136;}i:43;a:6:{i:0;s:59:"Request parsed with URL rule: backend/<controller>/<action>";i:1;i:8;i:2;s:29:"yii\web\UrlRule::parseRequest";i:3;d:1752318052.051337;i:4;a:0:{}i:5;i:8173208;}i:44;a:6:{i:0;a:3:{s:4:"rule";s:29:"backend/<controller>/<action>";s:5:"match";b:1;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752318052.05135;i:4;a:0:{}i:5;i:8173048;}}s:5:"route";s:28:"backend/worker-payment/index";s:6:"action";s:70:"app\modules\backend\controllers\WorkerPaymentController::actionIndex()";}";s:7:"request";s:8988:"a:15:{s:7:"flashes";a:0:{}s:10:"statusCode";i:200;s:14:"requestHeaders";a:11:{s:4:"host";s:6:"silver";s:10:"connection";s:10:"keep-alive";s:6:"pragma";s:8:"no-cache";s:13:"cache-control";s:8:"no-cache";s:25:"upgrade-insecure-requests";s:1:"1";s:10:"user-agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:6:"accept";s:135:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7";s:7:"referer";s:574:"http://silver/backend/worker-payment/index?worker_id=91&month=2025-07&payment_types%5B1%5D%5Bmethods%5D%5B%5D=1&payment_types%5B1%5D%5Bmethods%5D%5B%5D=4&payment_types%5B1%5D%5Bamount%5D=4%C2%A0177%C2%A0242&payment_types%5B3%5D%5Bmethods%5D%5B%5D=1&payment_types%5B3%5D%5Bamount%5D=100000&payment_types%5B4%5D%5Bmethods%5D%5B%5D=1&payment_types%5B4%5D%5Bamount%5D=100000&payment_types%5B5%5D%5Bmethods%5D%5B%5D=1&payment_types%5B5%5D%5Bamount%5D=100000&payment_types%5B7%5D%5Bmethods%5D%5B%5D=1&payment_types%5B7%5D%5Bamount%5D=100000&payment_types%5B6%5D%5Bamount%5D=100000";s:15:"accept-encoding";s:13:"gzip, deflate";s:15:"accept-language";s:65:"en-RU,en;q=0.9,ru-RU;q=0.8,ru;q=0.7,en-US;q=0.6,uz;q=0.5,hr;q=0.4";s:6:"cookie";s:539:"language=4e1a22a706b2b67a2ac68d2d85cb839925d09aed085ca214408c5498b87a9ea2a%3A2%3A%7Bi%3A0%3Bs%3A8%3A%22language%22%3Bi%3A1%3Bs%3A2%3A%22uz%22%3B%7D; PHPSESSID=tg16mblupj6u875l5vqjrkigi7pqr8f3; _identity=d3bbf1a40fbb07a5e71f4b657b0d9caaa7c20725eb7f86d2213424408f95f8caa%3A2%3A%7Bi%3A0%3Bs%3A9%3A%22_identity%22%3Bi%3A1%3Bs%3A16%3A%22%5B1%2Cnull%2C2592000%5D%22%3B%7D; _csrf=5de5c8623f8b1f477dee3e2ed0014819171dc78456feef3a897ed3d5ed083c16a%3A2%3A%7Bi%3A0%3Bs%3A5%3A%22_csrf%22%3Bi%3A1%3Bs%3A32%3A%22q6T-dtfJoJoonDmVVkMNyEhTWI1Kz4Cz%22%3B%7D";}s:15:"responseHeaders";a:8:{s:7:"Expires";s:29:"Thu, 19 Nov 1981 08:52:00 GMT";s:13:"Cache-Control";s:35:"no-store, no-cache, must-revalidate";s:6:"Pragma";s:8:"no-cache";s:12:"Content-Type";s:24:"text/html; charset=UTF-8";s:11:"X-Debug-Tag";s:13:"6872406409c63";s:16:"X-Debug-Duration";s:3:"210";s:12:"X-Debug-Link";s:37:"/debug/default/view?tag=6872406409c63";s:10:"Set-Cookie";s:260:"_identity=d3bbf1a40fbb07a5e71f4b657b0d9caaa7c20725eb7f86d2213424408f95f8caa%3A2%3A%7Bi%3A0%3Bs%3A9%3A%22_identity%22%3Bi%3A1%3Bs%3A16%3A%22%5B1%2Cnull%2C2592000%5D%22%3B%7D; expires=Mon, 11-Aug-2025 11:00:52 GMT; Max-Age=2592000; path=/; HttpOnly; SameSite=Lax";}s:5:"route";s:28:"backend/worker-payment/index";s:6:"action";s:70:"app\modules\backend\controllers\WorkerPaymentController::actionIndex()";s:12:"actionParams";a:0:{}s:7:"general";a:5:{s:6:"method";s:3:"GET";s:6:"isAjax";b:0;s:6:"isPjax";b:0;s:7:"isFlash";b:0;s:18:"isSecureConnection";b:0;}s:11:"requestBody";a:0:{}s:6:"SERVER";a:41:{s:15:"REDIRECT_STATUS";s:3:"200";s:9:"HTTP_HOST";s:6:"silver";s:15:"HTTP_CONNECTION";s:10:"keep-alive";s:11:"HTTP_PRAGMA";s:8:"no-cache";s:18:"HTTP_CACHE_CONTROL";s:8:"no-cache";s:30:"HTTP_UPGRADE_INSECURE_REQUESTS";s:1:"1";s:15:"HTTP_USER_AGENT";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:11:"HTTP_ACCEPT";s:135:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7";s:12:"HTTP_REFERER";s:574:"http://silver/backend/worker-payment/index?worker_id=91&month=2025-07&payment_types%5B1%5D%5Bmethods%5D%5B%5D=1&payment_types%5B1%5D%5Bmethods%5D%5B%5D=4&payment_types%5B1%5D%5Bamount%5D=4%C2%A0177%C2%A0242&payment_types%5B3%5D%5Bmethods%5D%5B%5D=1&payment_types%5B3%5D%5Bamount%5D=100000&payment_types%5B4%5D%5Bmethods%5D%5B%5D=1&payment_types%5B4%5D%5Bamount%5D=100000&payment_types%5B5%5D%5Bmethods%5D%5B%5D=1&payment_types%5B5%5D%5Bamount%5D=100000&payment_types%5B7%5D%5Bmethods%5D%5B%5D=1&payment_types%5B7%5D%5Bamount%5D=100000&payment_types%5B6%5D%5Bamount%5D=100000";s:20:"HTTP_ACCEPT_ENCODING";s:13:"gzip, deflate";s:20:"HTTP_ACCEPT_LANGUAGE";s:65:"en-RU,en;q=0.9,ru-RU;q=0.8,ru;q=0.7,en-US;q=0.6,uz;q=0.5,hr;q=0.4";s:11:"HTTP_COOKIE";s:539:"language=4e1a22a706b2b67a2ac68d2d85cb839925d09aed085ca214408c5498b87a9ea2a%3A2%3A%7Bi%3A0%3Bs%3A8%3A%22language%22%3Bi%3A1%3Bs%3A2%3A%22uz%22%3B%7D; PHPSESSID=tg16mblupj6u875l5vqjrkigi7pqr8f3; _identity=d3bbf1a40fbb07a5e71f4b657b0d9caaa7c20725eb7f86d2213424408f95f8caa%3A2%3A%7Bi%3A0%3Bs%3A9%3A%22_identity%22%3Bi%3A1%3Bs%3A16%3A%22%5B1%2Cnull%2C2592000%5D%22%3B%7D; _csrf=5de5c8623f8b1f477dee3e2ed0014819171dc78456feef3a897ed3d5ed083c16a%3A2%3A%7Bi%3A0%3Bs%3A5%3A%22_csrf%22%3Bi%3A1%3Bs%3A32%3A%22q6T-dtfJoJoonDmVVkMNyEhTWI1Kz4Cz%22%3B%7D";s:4:"PATH";s:343:"d:\ospanel\modules\php\PHP_8.1\ext;d:\ospanel\modules\php\PHP_8.1\pear;d:\ospanel\modules\php\PHP_8.1\pear\bin;d:\ospanel\modules\php\PHP_8.1;d:\ospanel\modules\wget\bin;d:\ospanel\modules\http\Apache_2.4-PHP_8.0-8.1\bin;d:\ospanel\modules\http\Apache_2.4-PHP_8.0-8.1;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\system32\Wbem;C:\WINDOWS\SysWOW64";s:10:"SystemRoot";s:10:"C:\WINDOWS";s:7:"COMSPEC";s:27:"C:\WINDOWS\system32\cmd.exe";s:7:"PATHEXT";s:62:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW";s:6:"WINDIR";s:10:"C:\WINDOWS";s:16:"SERVER_SIGNATURE";s:0:"";s:15:"SERVER_SOFTWARE";s:6:"Apache";s:11:"SERVER_NAME";s:6:"silver";s:11:"SERVER_ADDR";s:9:"127.0.0.1";s:11:"SERVER_PORT";s:2:"80";s:11:"REMOTE_ADDR";s:9:"127.0.0.1";s:13:"DOCUMENT_ROOT";s:34:"D:/OSPanel/domains/silverzavod/web";s:14:"REQUEST_SCHEME";s:4:"http";s:14:"CONTEXT_PREFIX";s:0:"";s:21:"CONTEXT_DOCUMENT_ROOT";s:34:"D:/OSPanel/domains/silverzavod/web";s:12:"SERVER_ADMIN";s:18:"[no address given]";s:15:"SCRIPT_FILENAME";s:44:"D:/OSPanel/domains/silverzavod/web/index.php";s:11:"REMOTE_PORT";s:5:"49351";s:12:"REDIRECT_URL";s:29:"/backend/worker-payment/index";s:21:"REDIRECT_QUERY_STRING";s:531:"worker_id=91&month=2025-07&payment_types%5B1%5D%5Bmethods%5D%5B%5D=1&payment_types%5B1%5D%5Bmethods%5D%5B%5D=4&payment_types%5B1%5D%5Bamount%5D=4%C2%A0177%C2%A0242&payment_types%5B3%5D%5Bmethods%5D%5B%5D=1&payment_types%5B3%5D%5Bamount%5D=100000&payment_types%5B4%5D%5Bmethods%5D%5B%5D=1&payment_types%5B4%5D%5Bamount%5D=100000&payment_types%5B5%5D%5Bmethods%5D%5B%5D=1&payment_types%5B5%5D%5Bamount%5D=100000&payment_types%5B7%5D%5Bmethods%5D%5B%5D=1&payment_types%5B7%5D%5Bamount%5D=100000&payment_types%5B6%5D%5Bamount%5D=100000";s:17:"GATEWAY_INTERFACE";s:7:"CGI/1.1";s:15:"SERVER_PROTOCOL";s:8:"HTTP/1.1";s:14:"REQUEST_METHOD";s:3:"GET";s:12:"QUERY_STRING";s:531:"worker_id=91&month=2025-07&payment_types%5B1%5D%5Bmethods%5D%5B%5D=1&payment_types%5B1%5D%5Bmethods%5D%5B%5D=4&payment_types%5B1%5D%5Bamount%5D=4%C2%A0177%C2%A0242&payment_types%5B3%5D%5Bmethods%5D%5B%5D=1&payment_types%5B3%5D%5Bamount%5D=100000&payment_types%5B4%5D%5Bmethods%5D%5B%5D=1&payment_types%5B4%5D%5Bamount%5D=100000&payment_types%5B5%5D%5Bmethods%5D%5B%5D=1&payment_types%5B5%5D%5Bamount%5D=100000&payment_types%5B7%5D%5Bmethods%5D%5B%5D=1&payment_types%5B7%5D%5Bamount%5D=100000&payment_types%5B6%5D%5Bamount%5D=100000";s:11:"REQUEST_URI";s:561:"/backend/worker-payment/index?worker_id=91&month=2025-07&payment_types%5B1%5D%5Bmethods%5D%5B%5D=1&payment_types%5B1%5D%5Bmethods%5D%5B%5D=4&payment_types%5B1%5D%5Bamount%5D=4%C2%A0177%C2%A0242&payment_types%5B3%5D%5Bmethods%5D%5B%5D=1&payment_types%5B3%5D%5Bamount%5D=100000&payment_types%5B4%5D%5Bmethods%5D%5B%5D=1&payment_types%5B4%5D%5Bamount%5D=100000&payment_types%5B5%5D%5Bmethods%5D%5B%5D=1&payment_types%5B5%5D%5Bamount%5D=100000&payment_types%5B7%5D%5Bmethods%5D%5B%5D=1&payment_types%5B7%5D%5Bamount%5D=100000&payment_types%5B6%5D%5Bamount%5D=100000";s:11:"SCRIPT_NAME";s:10:"/index.php";s:8:"PHP_SELF";s:10:"/index.php";s:18:"REQUEST_TIME_FLOAT";d:1752318051.932645;s:12:"REQUEST_TIME";i:1752318051;}s:3:"GET";a:3:{s:9:"worker_id";s:2:"91";s:5:"month";s:7:"2025-07";s:13:"payment_types";a:6:{i:1;a:2:{s:7:"methods";a:2:{i:0;s:1:"1";i:1;s:1:"4";}s:6:"amount";s:11:"4 177 242";}i:3;a:2:{s:7:"methods";a:1:{i:0;s:1:"1";}s:6:"amount";s:6:"100000";}i:4;a:2:{s:7:"methods";a:1:{i:0;s:1:"1";}s:6:"amount";s:6:"100000";}i:5;a:2:{s:7:"methods";a:1:{i:0;s:1:"1";}s:6:"amount";s:6:"100000";}i:7;a:2:{s:7:"methods";a:1:{i:0;s:1:"1";}s:6:"amount";s:6:"100000";}i:6;a:1:{s:6:"amount";s:6:"100000";}}}s:4:"POST";a:0:{}s:6:"COOKIE";a:4:{s:8:"language";s:102:"4e1a22a706b2b67a2ac68d2d85cb839925d09aed085ca214408c5498b87a9ea2a:2:{i:0;s:8:"language";i:1;s:2:"uz";}";s:9:"PHPSESSID";s:32:"tg16mblupj6u875l5vqjrkigi7pqr8f3";s:9:"_identity";s:118:"d3bbf1a40fbb07a5e71f4b657b0d9caaa7c20725eb7f86d2213424408f95f8caa:2:{i:0;s:9:"_identity";i:1;s:16:"[1,null,2592000]";}";s:5:"_csrf";s:130:"5de5c8623f8b1f477dee3e2ed0014819171dc78456feef3a897ed3d5ed083c16a:2:{i:0;s:5:"_csrf";i:1;s:32:"q6T-dtfJoJoonDmVVkMNyEhTWI1Kz4Cz";}";}s:5:"FILES";a:0:{}s:7:"SESSION";a:6:{s:7:"__flash";a:0:{}s:4:"__id";i:1;s:9:"__authKey";N;s:8:"__expire";i:1752325252;s:13:"last_activity";i:1752318052;s:7:"timeout";i:1800;}}";s:4:"user";s:2148:"a:5:{s:2:"id";i:1;s:8:"identity";a:8:{s:2:"id";s:1:"1";s:8:"username";s:7:"'admin'";s:9:"full_name";s:15:"'Administrator'";s:4:"role";s:1:"1";s:12:"access_token";s:42:"'Am4PA_XsYEchREdnLU19DkEKnev9Tqn03HNbrXNd'";s:8:"password";s:62:"'$2y$13$0eRlRvvyoXFPe1EZQr1a1OJtiuhoGEzSLGqLV0XWtF3ZVbwKTSkjO'";s:10:"created_at";s:21:"'2025-02-25 12:53:18'";s:10:"deleted_at";s:4:"null";}s:10:"attributes";a:8:{i:0;a:2:{s:9:"attribute";s:2:"id";s:5:"label";s:2:"ID";}i:1;a:2:{s:9:"attribute";s:8:"username";s:5:"label";s:10:"Логин";}i:2;a:2:{s:9:"attribute";s:9:"full_name";s:5:"label";s:6:"ФИО";}i:3;a:2:{s:9:"attribute";s:4:"role";s:5:"label";s:4:"Role";}i:4;a:2:{s:9:"attribute";s:12:"access_token";s:5:"label";s:12:"Access Token";}i:5;a:2:{s:9:"attribute";s:8:"password";s:5:"label";s:12:"Пароль";}i:6;a:2:{s:9:"attribute";s:10:"created_at";s:5:"label";s:25:"Дата создания";}i:7;a:2:{s:9:"attribute";s:10:"deleted_at";s:5:"label";s:25:"Дата удаления";}}s:13:"rolesProvider";O:26:"yii\data\ArrayDataProvider":12:{s:27:" yii\base\Component _events";a:0:{}s:35:" yii\base\Component _eventWildcards";a:0:{}s:30:" yii\base\Component _behaviors";N;s:2:"id";N;s:32:" yii\data\BaseDataProvider _sort";N;s:38:" yii\data\BaseDataProvider _pagination";N;s:32:" yii\data\BaseDataProvider _keys";N;s:34:" yii\data\BaseDataProvider _models";N;s:38:" yii\data\BaseDataProvider _totalCount";N;s:3:"key";N;s:9:"allModels";a:1:{s:5:"admin";a:7:{s:4:"type";i:1;s:4:"name";s:5:"admin";s:11:"description";s:13:"Administrator";s:8:"ruleName";N;s:4:"data";s:4:"null";s:9:"createdAt";i:**********;s:9:"updatedAt";i:**********;}}s:10:"modelClass";N;}s:19:"permissionsProvider";O:26:"yii\data\ArrayDataProvider":12:{s:27:" yii\base\Component _events";a:0:{}s:35:" yii\base\Component _eventWildcards";a:0:{}s:30:" yii\base\Component _behaviors";N;s:2:"id";s:4:"dp-1";s:32:" yii\data\BaseDataProvider _sort";N;s:38:" yii\data\BaseDataProvider _pagination";N;s:32:" yii\data\BaseDataProvider _keys";N;s:34:" yii\data\BaseDataProvider _models";N;s:38:" yii\data\BaseDataProvider _totalCount";N;s:3:"key";N;s:9:"allModels";a:0:{}s:10:"modelClass";N;}}";s:5:"asset";s:6568:"a:13:{s:26:"app\assets\DataTablesAsset";a:9:{s:10:"sourcePath";N;s:8:"basePath";s:34:"D:/OSPanel/domains/silverzavod/web";s:7:"baseUrl";s:0:"";s:7:"depends";a:3:{i:0;s:19:"yii\web\JqueryAsset";i:1;s:29:"yii\bootstrap4\BootstrapAsset";i:2;s:35:"yii\bootstrap4\BootstrapPluginAsset";}s:2:"js";a:3:{i:0;s:73:"https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.7/umd/popper.min.js";i:1;s:61:"https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js";i:2;s:65:"https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap4.min.js";}s:3:"css";a:1:{i:0;s:67:"https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap4.min.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:19:"yii\web\JqueryAsset";a:9:{s:10:"sourcePath";s:61:"D:\OSPanel\domains\silverzavod\vendor/bower-asset/jquery/dist";s:8:"basePath";s:50:"D:\OSPanel\domains\silverzavod\web\assets\e0ff1908";s:7:"baseUrl";s:16:"/assets/e0ff1908";s:7:"depends";a:0:{}s:2:"js";a:1:{i:0;s:9:"jquery.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:29:"yii\bootstrap4\BootstrapAsset";a:9:{s:10:"sourcePath";s:62:"D:\OSPanel\domains\silverzavod\vendor/npm-asset/bootstrap/dist";s:8:"basePath";s:50:"D:\OSPanel\domains\silverzavod\web\assets\c3f9d53c";s:7:"baseUrl";s:16:"/assets/c3f9d53c";s:7:"depends";a:0:{}s:2:"js";a:0:{}s:3:"css";a:1:{i:0;s:17:"css/bootstrap.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:35:"yii\bootstrap4\BootstrapPluginAsset";a:9:{s:10:"sourcePath";s:62:"D:\OSPanel\domains\silverzavod\vendor/npm-asset/bootstrap/dist";s:8:"basePath";s:50:"D:\OSPanel\domains\silverzavod\web\assets\c3f9d53c";s:7:"baseUrl";s:16:"/assets/c3f9d53c";s:7:"depends";a:2:{i:0;s:19:"yii\web\JqueryAsset";i:1;s:29:"yii\bootstrap4\BootstrapAsset";}s:2:"js";a:1:{i:0;s:22:"js/bootstrap.bundle.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:21:"yii\widgets\PjaxAsset";a:9:{s:10:"sourcePath";s:59:"D:\OSPanel\domains\silverzavod\vendor/bower-asset/yii2-pjax";s:8:"basePath";s:50:"D:\OSPanel\domains\silverzavod\web\assets\99d1aff7";s:7:"baseUrl";s:16:"/assets/99d1aff7";s:7:"depends";a:1:{i:0;s:16:"yii\web\YiiAsset";}s:2:"js";a:1:{i:0;s:14:"jquery.pjax.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:16:"yii\web\YiiAsset";a:9:{s:10:"sourcePath";s:57:"D:\OSPanel\domains\silverzavod\vendor\yiisoft\yii2/assets";s:8:"basePath";s:50:"D:\OSPanel\domains\silverzavod\web\assets\e0686f09";s:7:"baseUrl";s:16:"/assets/e0686f09";s:7:"depends";a:1:{i:0;s:19:"yii\web\JqueryAsset";}s:2:"js";a:1:{i:0;s:6:"yii.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:32:"/js/worker-payment/validation.js";a:9:{s:10:"sourcePath";N;s:8:"basePath";s:34:"D:/OSPanel/domains/silverzavod/web";s:7:"baseUrl";s:0:"";s:7:"depends";a:1:{i:0;s:19:"yii\web\JqueryAsset";}s:2:"js";a:1:{i:0;a:1:{i:0;s:31:"js/worker-payment/validation.js";}}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:34:"/js/worker-payment/calculations.js";a:9:{s:10:"sourcePath";N;s:8:"basePath";s:34:"D:/OSPanel/domains/silverzavod/web";s:7:"baseUrl";s:0:"";s:7:"depends";a:1:{i:0;s:19:"yii\web\JqueryAsset";}s:2:"js";a:1:{i:0;a:1:{i:0;s:33:"js/worker-payment/calculations.js";}}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:32:"/js/worker-payment/ui-helpers.js";a:9:{s:10:"sourcePath";N;s:8:"basePath";s:34:"D:/OSPanel/domains/silverzavod/web";s:7:"baseUrl";s:0:"";s:7:"depends";a:1:{i:0;s:19:"yii\web\JqueryAsset";}s:2:"js";a:1:{i:0;a:1:{i:0;s:31:"js/worker-payment/ui-helpers.js";}}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:35:"/js/worker-payment/ajax-requests.js";a:9:{s:10:"sourcePath";N;s:8:"basePath";s:34:"D:/OSPanel/domains/silverzavod/web";s:7:"baseUrl";s:0:"";s:7:"depends";a:1:{i:0;s:19:"yii\web\JqueryAsset";}s:2:"js";a:1:{i:0;a:1:{i:0;s:34:"js/worker-payment/ajax-requests.js";}}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:35:"/js/worker-payment/form-handlers.js";a:9:{s:10:"sourcePath";N;s:8:"basePath";s:34:"D:/OSPanel/domains/silverzavod/web";s:7:"baseUrl";s:0:"";s:7:"depends";a:1:{i:0;s:19:"yii\web\JqueryAsset";}s:2:"js";a:1:{i:0;a:1:{i:0;s:34:"js/worker-payment/form-handlers.js";}}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:26:"/js/worker-payment/main.js";a:9:{s:10:"sourcePath";N;s:8:"basePath";s:34:"D:/OSPanel/domains/silverzavod/web";s:7:"baseUrl";s:0:"";s:7:"depends";a:1:{i:0;s:19:"yii\web\JqueryAsset";}s:2:"js";a:1:{i:0;a:1:{i:0;s:25:"js/worker-payment/main.js";}}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:19:"app\assets\AppAsset";a:9:{s:10:"sourcePath";N;s:8:"basePath";s:34:"D:/OSPanel/domains/silverzavod/web";s:7:"baseUrl";s:0:"";s:7:"depends";a:2:{i:0;s:16:"yii\web\YiiAsset";i:1;s:19:"yii\web\JqueryAsset";}s:2:"js";a:18:{i:0;s:37:"modules/bootstrap/js/bootstrap.min.js";i:1;s:35:"modules/bootstrap-datepicker.min.js";i:2;s:43:"modules/nicescroll/jquery.nicescroll.min.js";i:3;s:21:"modules/moment.min.js";i:4;s:52:"modules/bootstrap-daterangepicker/daterangepicker.js";i:5;s:12:"js/stisla.js";i:6;s:35:"modules/izitoast/js/iziToast.min.js";i:7;s:38:"modules/select2/dist/js/select2.min.js";i:8;s:34:"modules/jquery-ui/jquery-ui.min.js";i:9;s:13:"js/scripts.js";i:10;s:26:"js/jquery.inputmask.min.js";i:11;s:47:"modules/chocolat/dist/js/jquery.chocolat.min.js";i:12;s:27:"js/jquery.magnific-popup.js";i:13;s:10:"js/menu.js";i:14;s:21:"modules/moment.min.js";i:15;s:59:"modules/bootstrap-timepicker/js/bootstrap-timepicker.min.js";i:16;s:64:"//cdnjs.cloudflare.com/ajax/libs/numeral.js/2.0.6/numeral.min.js";i:17;s:21:"js/input-formatter.js";}s:3:"css";a:12:{i:0;s:12:"css/site.css";i:1;s:39:"modules/bootstrap/css/bootstrap.min.css";i:2;s:53:"modules/bootstrap-daterangepicker/daterangepicker.css";i:3;s:36:"modules/bootstrap-datepicker.min.css";i:4;s:35:"modules/fontawesome/css/all.min.css";i:5;s:37:"modules/ionicons/css/ionicons.min.css";i:6;s:40:"modules/select2/dist/css/select2.min.css";i:7;s:37:"modules/izitoast/css/iziToast.min.css";i:8;s:13:"css/style.css";i:9;s:18:"css/components.css";i:10;s:22:"css/magnific-popup.css";i:11;s:61:"modules/bootstrap-timepicker/css/bootstrap-timepicker.min.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}}";s:7:"summary";a:13:{s:3:"tag";s:13:"6872406409c63";s:3:"url";s:574:"http://silver/backend/worker-payment/index?worker_id=91&month=2025-07&payment_types%5B1%5D%5Bmethods%5D%5B%5D=1&payment_types%5B1%5D%5Bmethods%5D%5B%5D=4&payment_types%5B1%5D%5Bamount%5D=4%C2%A0177%C2%A0242&payment_types%5B3%5D%5Bmethods%5D%5B%5D=1&payment_types%5B3%5D%5Bamount%5D=100000&payment_types%5B4%5D%5Bmethods%5D%5B%5D=1&payment_types%5B4%5D%5Bamount%5D=100000&payment_types%5B5%5D%5Bmethods%5D%5B%5D=1&payment_types%5B5%5D%5Bamount%5D=100000&payment_types%5B7%5D%5Bmethods%5D%5B%5D=1&payment_types%5B7%5D%5Bamount%5D=100000&payment_types%5B6%5D%5Bamount%5D=100000";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752318051.932645;s:10:"statusCode";i:200;s:8:"sqlCount";i:90;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:11428840;s:14:"processingTime";d:0.21056818962097168;}s:10:"exceptions";a:0:{}}