<?php

namespace app\modules\backend\services\worker_payment;

use app\common\models\Cashbox;
use app\common\models\CashboxDetail;
use app\common\models\PaymentType;
use app\modules\backend\models\WorkerFinances;
use app\modules\backend\models\WorkerSalary;
use app\modules\backend\models\Worker;
use app\modules\backend\models\WorkerDebt;
use app\common\models\Tracking;
use Yii;
use yii\db\Exception;
use yii\db\Transaction;
use app\modules\backend\models\Expenses;
use app\modules\backend\models\ExpensesType;

class WorkerPaymentService
{
    /**
     * Получить данные для формы создания выплаты
     */
    public function getFormData(): array
    {
        $workers = Worker::find()
            ->select(['id', 'full_name'])
            ->where(['deleted_at' => null])
            ->orderBy('full_name')
            ->asArray()
            ->all();

        $paymentTypes = WorkerFinances::getTypes();

        $cashboxes = Cashbox::find()
            ->where(['deleted_at' => null])
            ->orderBy('title')
            ->all();

        return [
            'workers' => $workers,
            'paymentTypes' => $paymentTypes,
            'cashboxes' => $cashboxes,
            'paymentTypeOptions' => PaymentType::getTypeLabels()
        ];
    }

    /**
     * Создать выплату работнику
     */
    public function createPayment(array $data): array
    {
        $transaction = Yii::$app->db->beginTransaction();
        
        try {
            // Валидация данных
            $validationResult = $this->validatePaymentData($data);
            if ($validationResult['status'] === 'error') {
                return $validationResult;
            }

            // Получение данных
            $worker = Worker::findOne($data['worker_id']);
            $paymentTypes = $data['payment_types'] ?? [];
            $month = $data['month'];

            // Создание записей о выплатах
            $createdPayments = [];
            $totalAmountByCashbox = []; // Группировка по кассам

            foreach ($paymentTypes as $typeId => $paymentType) {
                // Обработка разных форматов данных
                $amounts = [];
                if (!empty($paymentType['amount'])) {
                    // Простой формат - одна сумма
                    $amounts[PaymentType::CASH] = (float)$paymentType['amount']; // По умолчанию наличные
                } elseif (!empty($paymentType['amounts']) && is_array($paymentType['amounts'])) {
                    // Формат с разбивкой по методам оплаты
                    foreach ($paymentType['amounts'] as $methodType => $amount) {
                        $amounts[(int)$methodType] = (float)$amount;
                    }
                }

                foreach ($amounts as $paymentMethodType => $paymentAmount) {
                    if ($paymentAmount <= 0) continue;

                    // Определяем тип записи в worker_finances на основе типа выплаты и способа оплаты
                    $workerFinanceType = $this->getWorkerFinanceType($typeId, $paymentMethodType);

                    // Получаем подходящую кассу для данного типа платежа
                    $cashbox = $this->getAvailableCashboxForPaymentType($paymentMethodType);
                    if (!$cashbox) {
                        throw new Exception("Нет доступной кассы для типа оплаты: {$paymentMethodType}");
                    }

                    // Проверка баланса кассы
                    if ($cashbox->balance < $paymentAmount) {
                        throw new Exception("Недостаточно средств в кассе '{$cashbox->title}': доступно {$cashbox->balance}, требуется {$paymentAmount}");
                    }

                    // Создание записи о выплате
                    $workerFinance = new WorkerFinances();
                    $workerFinance->worker_id = $worker->id;
                    $workerFinance->month = $month;
                    $workerFinance->type = $workerFinanceType; // Используем правильный тип
                    $workerFinance->amount = $paymentAmount;
                    $workerFinance->description = $paymentType['description'] ?? '';
                    $workerFinance->created_at = date('Y-m-d H:i:s');

                    if (!$workerFinance->save()) {
                        throw new Exception('Ошибка при сохранении выплаты: ' . json_encode($workerFinance->errors));
                    }

                    // Создание записи в кассе
                    $cashboxDetail = new CashboxDetail();
                    $cashboxDetail->cashbox_id = $cashbox->id;
                    $cashboxDetail->amount = $paymentAmount;
                    $cashboxDetail->type = CashboxDetail::TYPE_OUT;
                    $cashboxDetail->worker_finance_id = $workerFinance->id;
                    $cashboxDetail->add_user_id = Yii::$app->user->id;
                    $cashboxDetail->created_at = date('Y-m-d H:i:s');

                    if (!$cashboxDetail->save()) {
                        throw new Exception('Ошибка при сохранении в кассе: ' . json_encode($cashboxDetail->errors));
                    }

                    // Создание tracking записи
                    $tracking = new Tracking();
                    $tracking->process_id = $workerFinance->id;
                    $tracking->progress_type = Tracking::PAY_FOR_WORKER;
                    $tracking->created_at = date('Y-m-d H:i:s');

                    if (!$tracking->save()) {
                        throw new Exception('Ошибка при создании tracking: ' . json_encode($tracking->errors));
                    }

                    // Создаем запись о расходе (expenses)
                    $expenseTypeId = $this->getExpenseTypeId($workerFinanceType);
                    if ($expenseTypeId !== null) {
                        $expense = new Expenses();
                        $expense->expense_type_id = $expenseTypeId;
                        $expense->add_user_id = Yii::$app->user->id;
                        $expense->payment_type = $paymentMethodType; // Тип оплаты (нал/безнал)
                        $expense->cashbox_id = $cashbox->id;
                        $expense->description = $paymentType['description'] ?? '';
                        $expense->summa = $paymentAmount;
                        $expense->worker_finance_id = $workerFinance->id;
                        $expense->status = Expenses::TYPE_ACCEPTED; // Выплаты сотрудникам всегда подтверждены
                        $expense->created_at = date('Y-m-d H:i:s');

                        if (!$expense->save()) {
                            throw new Exception('Ошибка при сохранении расхода: ' . json_encode($expense->errors));
                        }
                    }

                    $createdPayments[] = $workerFinance;

                    // Группируем суммы по кассам для обновления баланса
                    if (!isset($totalAmountByCashbox[$cashbox->id])) {
                        $totalAmountByCashbox[$cashbox->id] = ['cashbox' => $cashbox, 'amount' => 0];
                    }
                    $totalAmountByCashbox[$cashbox->id]['amount'] += $paymentAmount;
                }
            }

            // Обновление балансов касс
            foreach ($totalAmountByCashbox as $cashboxData) {
                $cashbox = $cashboxData['cashbox'];
                $amount = $cashboxData['amount'];

                $cashbox->balance -= $amount;
                if (!$cashbox->save()) {
                    throw new Exception("Ошибка при обновлении баланса кассы '{$cashbox->title}'");
                }
            }

            $transaction->commit();

            // Подсчитываем общую сумму
            $totalAmount = array_sum(array_column($totalAmountByCashbox, 'amount'));

            return [
                'status' => 'success',
                'message' => 'Выплата успешно создана',
                'payments' => $createdPayments,
                'total_amount' => $totalAmount
            ];

        } catch (\Exception $e) {
            $transaction->rollBack();
            return [
                'status' => 'error',
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Валидация данных для создания выплаты
     */
    private function validatePaymentData(array $data): array
    {
        // Если при обновлении необходимо игнорировать уже существующие выплаты за месяц
        $ignoreExistingPayments = $data['ignore_existing_payments'] ?? false;

        $errors = [];

        // Проверка обязательных полей
        if (empty($data['worker_id'])) {
            $errors['worker_id'] = 'Выберите сотрудника';
        }

        if (empty($data['month'])) {
            $errors['month'] = 'Выберите месяц';
        }

        if (empty($data['payment_types']) || !is_array($data['payment_types'])) {
            $errors['payment_types'] = 'Выберите хотя бы один тип выплаты';
        }

        // Получаем информацию о работнике для дальнейших проверок
        $worker = null;
        $workerSalaryInfo = null;
        if (!empty($data['worker_id'])) {
            $worker = Worker::findOne($data['worker_id']);
            if (!$worker) {
                $errors['worker_id'] = 'Сотрудник не найден';
            } else {
                $workerSalaryInfo = $this->getWorkerSalaryInfo($data['worker_id']);
                if ($workerSalaryInfo['status'] === 'error') {
                    $errors['worker_id'] = 'Не удалось получить информацию о зарплате работника';
                }
            }
        }

        // Проверка сумм и валидация лимитов зарплаты
        $totalAmount = 0;
        $salaryAndAdvanceAmount = 0;
        $hasDebtPayment = false;
        $debtPaymentAmount = 0;

        if (!empty($data['payment_types'])) {
            foreach ($data['payment_types'] as $typeId => $paymentType) {
                if (!empty($paymentType['amount']) || !empty($paymentType['amounts'])) {
                    // Обработка разных форматов данных (amount или amounts)
                    $amount = 0;
                    if (!empty($paymentType['amount'])) {
                        $amount = (float)$paymentType['amount'];
                    } elseif (!empty($paymentType['amounts']) && is_array($paymentType['amounts'])) {
                        foreach ($paymentType['amounts'] as $methodAmount) {
                            $amount += (float)$methodAmount;
                        }
                    }

                    if ($amount <= 0) {
                        $errors['payment_types'] = 'Сумма должна быть больше 0';
                        break;
                    }

                    // Проверка лимитов для зарплаты и аванса
                    if (($typeId == WorkerFinances::TYPE_SALARY || $typeId == WorkerFinances::TYPE_ADVANCE) && $workerSalaryInfo) {
                        // При обновлении старые выплаты будут удалены, поэтому их можно не учитывать
                        if ($ignoreExistingPayments) {
                            $paymentsInfo = [
                                'payments_by_type' => []
                            ];
                        } else {
                            $paymentsInfo = $this->getWorkerPaymentsForMonth($data['worker_id'], $data['month']);
                        }
                        $totalPaid = ($paymentsInfo['payments_by_type'][WorkerFinances::TYPE_SALARY] ?? 0) +
                                   ($paymentsInfo['payments_by_type'][WorkerFinances::TYPE_ADVANCE] ?? 0);
                        $remainingSalary = $workerSalaryInfo['salary'] - $totalPaid;

                        if ($amount > $remainingSalary) {
                            $errors['payment_amount_limit'] = 'Сумма выплаты не может превышать оставшуюся зарплату: ' . number_format($remainingSalary, 0, '.', ' ');
                        }

                        $salaryAndAdvanceAmount += $amount;
                    }

                    // Проверка погашения долга
                    if ($typeId == WorkerFinances::TYPE_DEBT_PAYMENT) {
                        $hasDebtPayment = true;
                        $debtPaymentAmount = $amount;
                    }

                    $totalAmount += $amount;
                }
            }
        }

        if ($totalAmount <= 0) {
            $errors['amount'] = 'Общая сумма должна быть больше 0';
        }

        // Валидация погашения долга
        if ($hasDebtPayment && $worker) {
            $debt = WorkerDebt::find()
                ->where(['worker_id' => $worker->id])
                ->andWhere(['>', 'amount', 0])
                ->andWhere(['deleted_at' => null])
                ->sum('amount') ?? 0;

            if ($debtPaymentAmount > $debt) {
                $errors['debt_payment'] = 'Сумма погашения долга не может превышать размер долга: ' . number_format($debt, 0, '.', ' ');
            }

            // Долг взыскивается только если пользователь выбрал это - никаких принудительных требований
        }

        // Валидация методов оплаты и касс
        $validationResult = $this->validatePaymentMethodsAndCashboxes($data);
        if ($validationResult['status'] === 'error') {
            $errors = array_merge($errors, $validationResult['errors']);
        }

        if (empty($errors)) {
            return [
                'status' => 'success',
                'message' => 'Валидация прошла успешно'
            ];
        } else {
            return [
                'status' => 'error',
                'errors' => $errors,
                'message' => 'Ошибки валидации'
            ];
        }
    }

    /**
     * Валидация методов оплаты и касс
     */
    private function validatePaymentMethodsAndCashboxes(array $data): array
    {
        $errors = [];
        $selectedPaymentTypes = [];

        // Собираем все выбранные методы оплаты
        if (!empty($data['payment_types'])) {
            foreach ($data['payment_types'] as $typeId => $paymentType) {
                if (!empty($paymentType['methods']) && is_array($paymentType['methods'])) {
                    foreach ($paymentType['methods'] as $method) {
                        if (!in_array($method, $selectedPaymentTypes)) {
                            $selectedPaymentTypes[] = (int)$method;
                        }
                    }
                } elseif (!empty($paymentType['amounts']) && is_array($paymentType['amounts'])) {
                    foreach (array_keys($paymentType['amounts']) as $method) {
                        if (!in_array($method, $selectedPaymentTypes)) {
                            $selectedPaymentTypes[] = (int)$method;
                        }
                    }
                }
            }
        }

        // Проверяем, что выбран хотя бы один метод оплаты
        if (empty($selectedPaymentTypes)) {
            $errors['payment_methods'] = 'Необходимо выбрать хотя бы один метод оплаты';
            return [
                'status' => 'error',
                'errors' => $errors
            ];
        }

        // Валидация доступности касс для выбранных методов оплаты
        foreach ($selectedPaymentTypes as $paymentType) {
            $availableCashbox = $this->getAvailableCashboxForPaymentType($paymentType);
            if (!$availableCashbox) {
                $paymentTypeName = $this->getPaymentTypeName($paymentType);
                $errors['cashbox_availability'] = "Нет доступной кассы для типа оплаты: {$paymentTypeName}";
            }
        }

        if (empty($errors)) {
            return [
                'status' => 'success'
            ];
        } else {
            return [
                'status' => 'error',
                'errors' => $errors
            ];
        }
    }

    /**
     * Получить доступную кассу для типа платежа
     */
    private function getAvailableCashboxForPaymentType(int $paymentType): ?Cashbox
    {
        // Основываясь на структуре касс:
        // Наличные (type=1): Оля касса (id=3, is_main=2) - приоритет для выплат работникам
        // Безналичные (type=2,3,4): Электронная касса (id=2, is_main=2)

        if ($paymentType == PaymentType::CASH) {
            // Приоритет для наличных выплат работникам - Оля касса
            return Cashbox::find()
                ->joinWith(['cashboxPaymentTypes' => function($query) use ($paymentType) {
                    $query->andWhere(['cashbox_payment_type.payment_type_id' => $paymentType]);
                }])
                ->where([
                    'cashbox.id' => 3,
                    'cashbox.is_main' => Cashbox::IS_SECONDARY,
                    'cashbox.deleted_at' => null
                ])
                ->one();
        } elseif (in_array($paymentType, [PaymentType::TRANSFER, PaymentType::TERMINAL, PaymentType::PAYMENT_CARD])) {
            // Для безналичных - Электронная касса
            return Cashbox::find()
                ->joinWith(['cashboxPaymentTypes' => function($query) use ($paymentType) {
                    $query->andWhere(['cashbox_payment_type.payment_type_id' => $paymentType]);
                }])
                ->where([
                    'cashbox.id' => 2,
                    'cashbox.is_main' => Cashbox::IS_SECONDARY,
                    'cashbox.deleted_at' => null
                ])
                ->one();
        }

        return null;
    }

    /**
     * Получить название типа платежа
     */
    private function getPaymentTypeName(int $paymentType): string
    {
        $typeNames = [
            PaymentType::CASH => 'Наличные',
            PaymentType::TRANSFER => 'Перевод',
            PaymentType::TERMINAL => 'Терминал',
            PaymentType::PAYMENT_CARD => 'Платежная карта'
        ];

        return $typeNames[$paymentType] ?? 'Неизвестный тип';
    }

    /**
     * Генерация описания для записи в кассе
     */
    private function generateCashboxDescription(Worker $worker, int $paymentType, string $month): string
    {
        $typeNames = [
            WorkerFinances::TYPE_SALARY => 'Зарплата',
            WorkerFinances::TYPE_ADVANCE => 'Аванс',
            WorkerFinances::TYPE_BONUS => 'Бонус',
            WorkerFinances::TYPE_DEBT => 'Долг',
            WorkerFinances::TYPE_ONE_TIME_PAYMENT => 'Разовая выплата',
            WorkerFinances::TYPE_DEBT_PAYMENT => 'Погашение долга',
            WorkerFinances::TYPE_VACATION_PAY => 'Отпускные',
        ];

        $typeName = $typeNames[$paymentType] ?? 'Выплата';
        $monthName = date('m.Y', strtotime($month . '-01'));

        return "{$typeName} для {$worker->full_name} за {$monthName}";
    }

    /**
     * Получить информацию о зарплате работника
     */
    public function getWorkerSalaryInfo(int $workerId): array
    {
        $worker = Worker::findOne($workerId);
        if (!$worker) {
            return [
                'status' => 'error',
                'message' => 'Сотрудник не найден'
            ];
        }

        $salary = WorkerSalary::find()
            ->where(['worker_id' => $workerId])
            ->andWhere(['end_date' => '9999-12-31'])
            ->andWhere(['deleted_at' => null])
            ->one();

        return [
            'status' => 'success',
            'worker' => $worker,
            'salary' => $salary ? $salary->amount : 0,
            'has_salary' => (bool)$salary
        ];
    }

    /**
     * Получить информацию о выплатах работника за месяц
     */
    public function getWorkerPaymentsForMonth(int $workerId, string $month): array
    {
        $payments = WorkerFinances::find()
            ->where(['worker_id' => $workerId])
            ->andWhere(['month' => $month])
            ->andWhere(['deleted_at' => null])
            ->all();

        $totalPaid = 0;
        $paymentsByType = [];

        foreach ($payments as $payment) {
            // Исключаем погашение долга из общей суммы выплат работнику
            if ($payment->type != WorkerFinances::TYPE_DEBT_PAYMENT) {
                $totalPaid += $payment->amount;
            }
            if (!isset($paymentsByType[$payment->type])) {
                $paymentsByType[$payment->type] = 0;
            }
            $paymentsByType[$payment->type] += $payment->amount;
        }

        return [
            'status' => 'success',
            'payments' => $payments,
            'total_paid' => $totalPaid,
            'payments_by_type' => $paymentsByType
        ];
    }

    /**
     * Определить тип записи в worker_finances на основе типа выплаты и способа оплаты
     */
    private function getWorkerFinanceType(int $paymentTypeId, int $paymentMethodType): int
    {
        // Для всех типов выплат возвращаем исходный тип
        return $paymentTypeId;
    }

    /**
     * Определить ID типа расхода по типу выплаты работнику
     * Использует наименования, созданные в миграциях: Oylik, Avans, Bonus, Debt, One time payment, Vacation pay
     */
    private function getExpenseTypeId(int $workerFinanceType): ?int
    {
        static $cache = [];

        if (isset($cache[$workerFinanceType])) {
            return $cache[$workerFinanceType];
        }

        $mapping = [
            WorkerFinances::TYPE_SALARY         => 'Oylik',
            WorkerFinances::TYPE_ADVANCE        => 'Avans',
            WorkerFinances::TYPE_BONUS          => 'Bonus',
            WorkerFinances::TYPE_DEBT           => 'Debt',
            WorkerFinances::TYPE_ONE_TIME_PAYMENT => 'One time payment',
            WorkerFinances::TYPE_VACATION_PAY   => 'Vacation pay',
        ];

        if (!isset($mapping[$workerFinanceType])) {
            return null; // Неизвестный тип – расход не создаём
        }

        $expenseType = ExpensesType::find()->where(['name' => $mapping[$workerFinanceType]])->one();

        if (!$expenseType) {
            throw new Exception("Тип расходов '{$mapping[$workerFinanceType]}' не найден в базе данных");
        }

        $cache[$workerFinanceType] = (int)$expenseType->id;
        return $cache[$workerFinanceType];
    }
}
