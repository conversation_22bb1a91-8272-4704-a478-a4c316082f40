<?php

namespace app\modules\backend\controllers;

use app\modules\backend\models\Worker;
use app\modules\backend\models\WorkerFinances;
use app\modules\backend\models\WorkerDebt;
use app\modules\backend\models\WorkerSalary;
use Yii;
use yii\web\Response;

// Подключаем хелпер для работы с датами
require_once Yii::getAlia<PERSON>('@app/helpers/date-formatter.php');

class WorkerPaymentController extends BaseController
{
    public function actionIndex()
    {
        $month = Yii::$app->request->get('month', date('Y-m'));
        $currentLanguage = getCurrentLanguage();
        $monthName = formatMonthName($month, $currentLanguage);
        
        $sql = "SELECT
            w.id as worker_id,
            w.full_name,
            p.name as position,
            ws.amount as salary,

            -- Бонус безналичный за месяц
            COALESCE(SUM(CASE WHEN wf.type = :bonus_type AND wf.month = :month THEN wf.amount END), 0) as bonus,

            -- Бонус наличный за месяц
            COALESCE(SUM(CASE WHEN wf.type = :cash_bonus_type AND wf.month = :month THEN wf.amount END), 0) as cash_bonus,

            -- Отпускные безналичные за месяц
            COALESCE(SUM(CASE WHEN wf.type = :vacation_type AND wf.month = :month THEN wf.amount END), 0) as vacation_pay,

            -- Отпускные наличные за месяц
            COALESCE(SUM(CASE WHEN wf.type = :cash_vacation_type AND wf.month = :month THEN wf.amount END), 0) as cash_vacation_pay,

            -- Аванс безналичный за месяц
            COALESCE(SUM(CASE WHEN wf.type = :advance_type AND wf.month = :month THEN wf.amount END), 0) as advance,

            -- Аванс наличный за месяц
            COALESCE(SUM(CASE WHEN wf.type = :cash_advance_type AND wf.month = :month THEN wf.amount END), 0) as cash_advance,

            -- Разовые выплаты безналичные за месяц
            COALESCE(SUM(CASE WHEN wf.type = :one_time_payment_type AND wf.month = :month THEN wf.amount END), 0) as one_time_payment,

            -- Разовые выплаты наличные за месяц
            COALESCE(SUM(CASE WHEN wf.type = :cash_one_time_payment_type AND wf.month = :month THEN wf.amount END), 0) as cash_one_time_payment,

            -- Оплата по пластиковой карте (зарплата безналичная)
            COALESCE(SUM(CASE WHEN wf.type = :salary_type AND wf.month = :month THEN wf.amount END), 0) as card_payment,

            -- Оплата наличными (зарплата наличная)
            COALESCE(SUM(CASE WHEN wf.type = :cash_salary_type AND wf.month = :month THEN wf.amount END), 0) as cash_payment,
            
            -- Долг (общий долг минус все погашения)
            GREATEST(COALESCE(wd.total_debt, 0) - COALESCE(debt_payments.total_payments, 0), 0) as debt,
            
            -- Удержания долга в этом месяце
            COALESCE(SUM(CASE WHEN wf.type = :debt_payment_type AND wf.month = :month THEN wf.amount END), 0) as debt_deduction,
            
            -- Получено на руки за месяц (фактически выплачено работнику)
            COALESCE(SUM(CASE WHEN wf.type = :salary_type AND wf.month = :month THEN wf.amount END), 0) + 
            COALESCE(SUM(CASE WHEN wf.type = :cash_salary_type AND wf.month = :month THEN wf.amount END), 0) + 
            COALESCE(SUM(CASE WHEN wf.type = :bonus_type AND wf.month = :month THEN wf.amount END), 0) + 
            COALESCE(SUM(CASE WHEN wf.type = :vacation_type AND wf.month = :month THEN wf.amount END), 0) - 
            COALESCE(SUM(CASE WHEN wf.type = :debt_payment_type AND wf.month = :month THEN wf.amount END), 0) as received_amount
            
        FROM worker w
        LEFT JOIN position p ON p.id = w.position_id
        LEFT JOIN worker_salary ws ON ws.worker_id = w.id AND ws.end_date = '9999-12-31'
        LEFT JOIN worker_finances wf ON wf.worker_id = w.id AND wf.deleted_at IS NULL
        LEFT JOIN (
            SELECT 
                worker_id,
                SUM(amount) as total_debt
            FROM worker_debt 
            WHERE deleted_at IS NULL AND (status IS NULL OR status = false)
            GROUP BY worker_id
        ) wd ON wd.worker_id = w.id
        LEFT JOIN (
            SELECT 
                worker_id,
                SUM(amount) as total_payments
            FROM worker_finances 
            WHERE type = '6' AND deleted_at IS NULL
            GROUP BY worker_id
        ) debt_payments ON debt_payments.worker_id = w.id
        WHERE w.deleted_at IS NULL
        GROUP BY w.id, w.full_name, p.name, ws.amount, wd.total_debt, debt_payments.total_payments
        ORDER BY w.full_name";
        
        $command = Yii::$app->db->createCommand($sql);
        $command->bindValue(':month', $month);
        $command->bindValue(':bonus_type', WorkerFinances::TYPE_BONUS);
        $command->bindValue(':cash_bonus_type', WorkerFinances::TYPE_CASH_BONUS);
        $command->bindValue(':vacation_type', WorkerFinances::TYPE_VACATION_PAY);
        $command->bindValue(':cash_vacation_type', WorkerFinances::TYPE_CASH_VACATION_PAY);
        $command->bindValue(':advance_type', WorkerFinances::TYPE_ADVANCE);
        $command->bindValue(':cash_advance_type', WorkerFinances::TYPE_CASH_ADVANCE);
        $command->bindValue(':one_time_payment_type', WorkerFinances::TYPE_ONE_TIME_PAYMENT);
        $command->bindValue(':cash_one_time_payment_type', WorkerFinances::TYPE_CASH_ONE_TIME_PAYMENT);
        $command->bindValue(':salary_type', WorkerFinances::TYPE_SALARY);
        $command->bindValue(':cash_salary_type', WorkerFinances::TYPE_CASH_SALARY);
        $command->bindValue(':debt_payment_type', WorkerFinances::TYPE_DEBT_PAYMENT);
        
        $result = $command->queryAll();
        
        // Добавляем проверку наличия выплат для каждого работника
        $updateService = new \app\modules\backend\services\worker_payment\WorkerPaymentUpdateService();
        foreach ($result as &$worker) {
            $existingPayments = $updateService->getExistingPayments($worker['worker_id'], $month);
            $worker['has_payments'] = $existingPayments['status'] === 'success' && $existingPayments['count'] > 0;
        }
        
        return $this->render('index', [
            'result' => $result,
            'month' => $month,
            'monthName' => $monthName
        ]);
    }
    
    public function actionSearch()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        $month = Yii::$app->request->post('month', date('Y-m'));
        
      
        $sql = "SELECT
            w.id as worker_id,
            w.full_name,
            p.name as position,
            ws.amount as salary,

            -- Бонус безналичный за месяц
            COALESCE(SUM(CASE WHEN wf.type = :bonus_type AND wf.month = :month THEN wf.amount END), 0) as bonus,

            -- Бонус наличный за месяц
            COALESCE(SUM(CASE WHEN wf.type = :cash_bonus_type AND wf.month = :month THEN wf.amount END), 0) as cash_bonus,

            -- Отпускные безналичные за месяц
            COALESCE(SUM(CASE WHEN wf.type = :vacation_type AND wf.month = :month THEN wf.amount END), 0) as vacation_pay,

            -- Отпускные наличные за месяц
            COALESCE(SUM(CASE WHEN wf.type = :cash_vacation_type AND wf.month = :month THEN wf.amount END), 0) as cash_vacation_pay,

            -- Аванс безналичный за месяц
            COALESCE(SUM(CASE WHEN wf.type = :advance_type AND wf.month = :month THEN wf.amount END), 0) as advance,

            -- Аванс наличный за месяц
            COALESCE(SUM(CASE WHEN wf.type = :cash_advance_type AND wf.month = :month THEN wf.amount END), 0) as cash_advance,

            -- Разовые выплаты безналичные за месяц
            COALESCE(SUM(CASE WHEN wf.type = :one_time_payment_type AND wf.month = :month THEN wf.amount END), 0) as one_time_payment,

            -- Разовые выплаты наличные за месяц
            COALESCE(SUM(CASE WHEN wf.type = :cash_one_time_payment_type AND wf.month = :month THEN wf.amount END), 0) as cash_one_time_payment,

            -- Оплата по пластиковой карте (зарплата безналичная)
            COALESCE(SUM(CASE WHEN wf.type = :salary_type AND wf.month = :month THEN wf.amount END), 0) as card_payment,

            -- Оплата наличными (зарплата наличная)
            COALESCE(SUM(CASE WHEN wf.type = :cash_salary_type AND wf.month = :month THEN wf.amount END), 0) as cash_payment,
            
            -- Долг (общий долг минус все погашения)
            GREATEST(COALESCE(wd.total_debt, 0) - COALESCE(debt_payments.total_payments, 0), 0) as debt,
            
            -- Удержания долга в этом месяце
            COALESCE(SUM(CASE WHEN wf.type = :debt_payment_type AND wf.month = :month THEN wf.amount END), 0) as debt_deduction,
            
            -- Получено на руки за месяц (фактически выплачено работнику)
            COALESCE(SUM(CASE WHEN wf.type = :salary_type AND wf.month = :month THEN wf.amount END), 0) + 
            COALESCE(SUM(CASE WHEN wf.type = :cash_salary_type AND wf.month = :month THEN wf.amount END), 0) + 
            COALESCE(SUM(CASE WHEN wf.type = :bonus_type AND wf.month = :month THEN wf.amount END), 0) + 
            COALESCE(SUM(CASE WHEN wf.type = :vacation_type AND wf.month = :month THEN wf.amount END), 0) - 
            COALESCE(SUM(CASE WHEN wf.type = :debt_payment_type AND wf.month = :month THEN wf.amount END), 0) as received_amount
            
        FROM worker w
        LEFT JOIN position p ON p.id = w.position_id
        LEFT JOIN worker_salary ws ON ws.worker_id = w.id AND ws.end_date = '9999-12-31'
        LEFT JOIN worker_finances wf ON wf.worker_id = w.id AND wf.deleted_at IS NULL
        LEFT JOIN (
            SELECT 
                worker_id,
                SUM(amount) as total_debt
            FROM worker_debt 
            WHERE deleted_at IS NULL AND (status IS NULL OR status = false)
            GROUP BY worker_id
        ) wd ON wd.worker_id = w.id
        LEFT JOIN (
            SELECT 
                worker_id,
                SUM(amount) as total_payments
            FROM worker_finances 
            WHERE type = '6' AND deleted_at IS NULL
            GROUP BY worker_id
        ) debt_payments ON debt_payments.worker_id = w.id
        WHERE w.deleted_at IS NULL
        GROUP BY w.id, w.full_name, p.name, ws.amount, wd.total_debt, debt_payments.total_payments
        ORDER BY w.full_name";
        
        $command = Yii::$app->db->createCommand($sql);
        $command->bindValue(':month', $month);
        $command->bindValue(':bonus_type', WorkerFinances::TYPE_BONUS);
        $command->bindValue(':cash_bonus_type', WorkerFinances::TYPE_CASH_BONUS);
        $command->bindValue(':vacation_type', WorkerFinances::TYPE_VACATION_PAY);
        $command->bindValue(':cash_vacation_type', WorkerFinances::TYPE_CASH_VACATION_PAY);
        $command->bindValue(':advance_type', WorkerFinances::TYPE_ADVANCE);
        $command->bindValue(':cash_advance_type', WorkerFinances::TYPE_CASH_ADVANCE);
        $command->bindValue(':one_time_payment_type', WorkerFinances::TYPE_ONE_TIME_PAYMENT);
        $command->bindValue(':cash_one_time_payment_type', WorkerFinances::TYPE_CASH_ONE_TIME_PAYMENT);
        $command->bindValue(':salary_type', WorkerFinances::TYPE_SALARY);
        $command->bindValue(':cash_salary_type', WorkerFinances::TYPE_CASH_SALARY);
        $command->bindValue(':debt_payment_type', WorkerFinances::TYPE_DEBT_PAYMENT);
        
        $result = $command->queryAll();
        
        // Добавляем проверку наличия выплат для каждого работника
        $updateService = new \app\modules\backend\services\worker_payment\WorkerPaymentUpdateService();
        foreach ($result as &$worker) {
            $existingPayments = $updateService->getExistingPayments($worker['worker_id'], $month);
            $worker['has_payments'] = $existingPayments['status'] === 'success' && $existingPayments['count'] > 0;
        }
        
        return [
            'status' => 'success',
            'data' => $result
        ];
    }
    
    public function actionCreate()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        
        $service = new \app\modules\backend\services\worker_payment\WorkerPaymentService();
        $formData = $service->getFormData();
        
        return [
            "status" => true,
            "content" => $this->renderPartial('create', $formData)
        ];
    }
    
    public function actionStore()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        
        $service = new \app\modules\backend\services\worker_payment\WorkerPaymentService();
        $result = $service->createPayment(Yii::$app->request->post());
        
        return $result;
    }
    
    public function actionGetWorkerInfo()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        
        $workerId = Yii::$app->request->get('worker_id');
        $month = Yii::$app->request->get('month', date('Y-m'));
        
        if (!$workerId) {
            return [
                'status' => 'error',
                'message' => 'Не указан ID работника'
            ];
        }

        $service = new \app\modules\backend\services\worker_payment\WorkerPaymentService();
        $workerInfo = $service->getWorkerSalaryInfo($workerId);
        $paymentsInfo = $service->getWorkerPaymentsForMonth($workerId, $month);

        if ($workerInfo['status'] === 'error') {
            return $workerInfo;
        }

        // Получаем информацию о долге
        $debt = WorkerDebt::find()
            ->where(['worker_id' => $workerId])
            ->andWhere(['>', 'amount', 0])
            ->andWhere(['deleted_at' => null])
            ->sum('amount') ?? 0;

        return [
            'status' => 'success',
            'worker' => $workerInfo['worker'],
            'salary' => $workerInfo['salary'],
            'has_salary' => $workerInfo['has_salary'],
            'payments' => $paymentsInfo['payments_by_type'] ?? [],
            'payment_methods_by_type' => $this->getPaymentMethodsByType($workerId, $month),
            'total_paid' => $paymentsInfo['total_paid'] ?? 0,
            'debt' => $debt
        ];
    }
    
    public function actionExport()
    {
        $month = Yii::$app->request->get('month', date('Y-m'));
        
        $sql = "SELECT
            w.id as worker_id,
            w.full_name,
            p.name as position,
            ws.amount as salary,

            -- Бонус безналичный за месяц
            COALESCE(SUM(CASE WHEN wf.type = :bonus_type AND wf.month = :month THEN wf.amount END), 0) as bonus,

            -- Бонус наличный за месяц
            COALESCE(SUM(CASE WHEN wf.type = :cash_bonus_type AND wf.month = :month THEN wf.amount END), 0) as cash_bonus,

            -- Отпускные безналичные за месяц
            COALESCE(SUM(CASE WHEN wf.type = :vacation_type AND wf.month = :month THEN wf.amount END), 0) as vacation_pay,

            -- Отпускные наличные за месяц
            COALESCE(SUM(CASE WHEN wf.type = :cash_vacation_type AND wf.month = :month THEN wf.amount END), 0) as cash_vacation_pay,

            -- Аванс безналичный за месяц
            COALESCE(SUM(CASE WHEN wf.type = :advance_type AND wf.month = :month THEN wf.amount END), 0) as advance,

            -- Аванс наличный за месяц
            COALESCE(SUM(CASE WHEN wf.type = :cash_advance_type AND wf.month = :month THEN wf.amount END), 0) as cash_advance,

            -- Разовые выплаты безналичные за месяц
            COALESCE(SUM(CASE WHEN wf.type = :one_time_payment_type AND wf.month = :month THEN wf.amount END), 0) as one_time_payment,

            -- Разовые выплаты наличные за месяц
            COALESCE(SUM(CASE WHEN wf.type = :cash_one_time_payment_type AND wf.month = :month THEN wf.amount END), 0) as cash_one_time_payment,

            -- Оплата по пластиковой карте (зарплата безналичная)
            COALESCE(SUM(CASE WHEN wf.type = :salary_type AND wf.month = :month THEN wf.amount END), 0) as card_payment,

            -- Оплата наличными (зарплата наличная)
            COALESCE(SUM(CASE WHEN wf.type = :cash_salary_type AND wf.month = :month THEN wf.amount END), 0) as cash_payment,
            
            -- Долг (непогашенные долги)
            COALESCE(wd.total_debt, 0) as debt,
            
            -- Удержания долга в этом месяце
            COALESCE(SUM(CASE WHEN wf.type = :debt_payment_type AND wf.month = :month THEN wf.amount END), 0) as debt_deduction,
            
            -- Вычисляем получено на руки
            COALESCE(ws.amount, 0) + 
            COALESCE(SUM(CASE WHEN wf.type = :bonus_type AND wf.month = :month THEN wf.amount END), 0) + 
            COALESCE(SUM(CASE WHEN wf.type = :vacation_type AND wf.month = :month THEN wf.amount END), 0) - 
            COALESCE(SUM(CASE WHEN wf.type = :advance_type AND wf.month = :month THEN wf.amount END), 0) - 
            COALESCE(SUM(CASE WHEN wf.type = :debt_payment_type AND wf.month = :month THEN wf.amount END), 0) as received_amount
            
        FROM worker w
        LEFT JOIN position p ON p.id = w.position_id
        LEFT JOIN worker_salary ws ON ws.worker_id = w.id AND ws.end_date = '9999-12-31'
        LEFT JOIN worker_finances wf ON wf.worker_id = w.id AND wf.deleted_at IS NULL
        LEFT JOIN (
            SELECT 
                worker_id,
                SUM(amount) as total_debt
            FROM worker_debt 
            WHERE deleted_at IS NULL AND (status IS NULL OR status = false)
            GROUP BY worker_id
        ) wd ON wd.worker_id = w.id
        WHERE w.deleted_at IS NULL
        GROUP BY w.id, w.full_name, p.name, ws.amount, wd.total_debt
        ORDER BY w.full_name";
        
        $command = Yii::$app->db->createCommand($sql);
        $command->bindValue(':month', $month);
        $command->bindValue(':bonus_type', WorkerFinances::TYPE_BONUS);
        $command->bindValue(':cash_bonus_type', WorkerFinances::TYPE_CASH_BONUS);
        $command->bindValue(':vacation_type', WorkerFinances::TYPE_VACATION_PAY);
        $command->bindValue(':cash_vacation_type', WorkerFinances::TYPE_CASH_VACATION_PAY);
        $command->bindValue(':advance_type', WorkerFinances::TYPE_ADVANCE);
        $command->bindValue(':cash_advance_type', WorkerFinances::TYPE_CASH_ADVANCE);
        $command->bindValue(':one_time_payment_type', WorkerFinances::TYPE_ONE_TIME_PAYMENT);
        $command->bindValue(':cash_one_time_payment_type', WorkerFinances::TYPE_CASH_ONE_TIME_PAYMENT);
        $command->bindValue(':salary_type', WorkerFinances::TYPE_SALARY);
        $command->bindValue(':cash_salary_type', WorkerFinances::TYPE_CASH_SALARY);
        $command->bindValue(':debt_payment_type', WorkerFinances::TYPE_DEBT_PAYMENT);
        
        $result = $command->queryAll();
        
        // Подготавливаем данные для Excel
        $data = [];
        $data[] = [
            'ФИО',
            'Должность',
            'Оклад',
            'Бонус',
            'Отпускные',
            'Аванс',
            'Оплата по карте',
            'Долг',
            'Удержания долга',
            'Получено на руки',
            'Роспись'
        ];
        
        foreach ($result as $row) {
            $totalBonusAndOther = ($row['bonus'] ?? 0) + ($row['cash_bonus'] ?? 0) + ($row['vacation_pay'] ?? 0) + ($row['cash_vacation_pay'] ?? 0) + ($row['one_time_payment'] ?? 0) + ($row['cash_one_time_payment'] ?? 0);
            $totalVacationPay = ($row['vacation_pay'] ?? 0) + ($row['cash_vacation_pay'] ?? 0);
            $totalAdvanceWithCash = ($row['advance'] ?? 0) + ($row['cash_advance'] ?? 0) + ($row['cash_payment'] ?? 0);
            $totalCardPayment = ($row['card_payment'] ?? 0) + ($row['bonus'] ?? 0) + ($row['vacation_pay'] ?? 0) + ($row['advance'] ?? 0) + ($row['one_time_payment'] ?? 0);

            $data[] = [
                $row['full_name'],
                $row['position'],
                $row['salary'] ?? 0,
                $totalBonusAndOther,
                $totalVacationPay,
                $totalAdvanceWithCash,
                $totalCardPayment,
                $row['debt'] ?? 0,
                $row['debt_deduction'] ?? 0,
                $row['received_amount'] ?? 0,
                '' // Пустая колонка для росписи
            ];
        }
        
        // Создаем CSV файл
        $filename = 'worker_payment_report_' . $month . '.csv';
        
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        $output = fopen('php://output', 'w');
        
        // Добавляем BOM для корректного отображения в Excel
        fwrite($output, "\xEF\xBB\xBF");
        
        foreach ($data as $row) {
            fputcsv($output, $row, ';');
        }
        
        fclose($output);
        exit;
    }
    
    public function actionEdit()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        
        $workerId = Yii::$app->request->get('worker_id');
        $month = Yii::$app->request->get('month', date('Y-m'));
        
        if (!$workerId) {
            return [
                'status' => 'error',
                'message' => 'Не указан ID работника'
            ];
        }
        
        $updateService = new \app\modules\backend\services\worker_payment\WorkerPaymentUpdateService();
        $formData = $updateService->getEditFormData($workerId, $month);
        
        if (isset($formData['status']) && $formData['status'] === 'error') {
            return $formData;
        }
        
        return [
            "status" => true,
            "content" => $this->renderPartial('edit', $formData)
        ];
    }
    
    public function actionUpdate()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        
        $updateService = new \app\modules\backend\services\worker_payment\WorkerPaymentUpdateService();
        $result = $updateService->updatePayment(Yii::$app->request->post());
        
        return $result;
    }

    /**
     * Возвращает массив разбивки выплат по базовому типу и методу оплаты
     */
    private function getPaymentMethodsByType($workerId, $month)
    {
        $payments = WorkerFinances::find()
            ->where([
                'worker_id' => $workerId,
                'month' => $month,
                'deleted_at' => null
            ])->all();

        $result = [];

        foreach ($payments as $payment) {
            $baseType = $payment->type;
            $methodId = \app\common\models\PaymentType::PAYMENT_CARD; // По умолчанию безналичный

            // Определяем базовый тип и метод оплаты
            switch ($payment->type) {
                case WorkerFinances::TYPE_CASH_SALARY:
                    $baseType = WorkerFinances::TYPE_SALARY;
                    $methodId = \app\common\models\PaymentType::CASH;
                    break;
                case WorkerFinances::TYPE_SALARY:
                    $baseType = WorkerFinances::TYPE_SALARY;
                    $methodId = \app\common\models\PaymentType::PAYMENT_CARD;
                    break;
                case WorkerFinances::TYPE_CASH_ADVANCE:
                    $baseType = WorkerFinances::TYPE_ADVANCE;
                    $methodId = \app\common\models\PaymentType::CASH;
                    break;
                case WorkerFinances::TYPE_ADVANCE:
                    $baseType = WorkerFinances::TYPE_ADVANCE;
                    $methodId = \app\common\models\PaymentType::PAYMENT_CARD;
                    break;
                case WorkerFinances::TYPE_CASH_BONUS:
                    $baseType = WorkerFinances::TYPE_BONUS;
                    $methodId = \app\common\models\PaymentType::CASH;
                    break;
                case WorkerFinances::TYPE_BONUS:
                    $baseType = WorkerFinances::TYPE_BONUS;
                    $methodId = \app\common\models\PaymentType::PAYMENT_CARD;
                    break;
                case WorkerFinances::TYPE_CASH_ONE_TIME_PAYMENT:
                    $baseType = WorkerFinances::TYPE_ONE_TIME_PAYMENT;
                    $methodId = \app\common\models\PaymentType::CASH;
                    break;
                case WorkerFinances::TYPE_ONE_TIME_PAYMENT:
                    $baseType = WorkerFinances::TYPE_ONE_TIME_PAYMENT;
                    $methodId = \app\common\models\PaymentType::PAYMENT_CARD;
                    break;
                case WorkerFinances::TYPE_CASH_VACATION_PAY:
                    $baseType = WorkerFinances::TYPE_VACATION_PAY;
                    $methodId = \app\common\models\PaymentType::CASH;
                    break;
                case WorkerFinances::TYPE_VACATION_PAY:
                    $baseType = WorkerFinances::TYPE_VACATION_PAY;
                    $methodId = \app\common\models\PaymentType::PAYMENT_CARD;
                    break;
                default:
                    // Для остальных типов оставляем как есть
                    $baseType = $payment->type;
                    $methodId = \app\common\models\PaymentType::CASH;
                    break;
            }

            if (!isset($result[$baseType])) {
                $result[$baseType] = [];
            }

            if (!isset($result[$baseType][$methodId])) {
                $result[$baseType][$methodId] = 0;
            }

            $result[$baseType][$methodId] += $payment->amount;
        }

        return $result;
    }
}
